# Block Automation Bot (block_automation.py)

## Overview
A Minecraft automation script that continuously alternates between placing and breaking blocks. Features a modern CustomTkinter GUI with speed controls, safety mechanisms, and comprehensive statistics tracking.

## Features

### Core Functionality
- **Automated Block Cycle**: Continuously places blocks (right-click) and breaks them (left-click)
- **High-Precision Timing**: Adjustable delay between actions (0-100ms range with 1ms precision)
- **Safety Boundaries**: Y-coordinate limits (60-320) to prevent dangerous operations
- **Emergency Stop**: Immediate halt of all automation with audio alerts

### Modern GUI Interface
- **CustomTkinter Design**: Modern, dark-themed interface with card-based layout
- **Real-time Status**: Live updates of automation state and current action
- **Speed Control**: Interactive slider for delay adjustment with real-time feedback
- **Statistics Display**: Session and total statistics with blocks placed/broken counts
- **Scrollable Layout**: Responsive design that adapts to different window sizes

### Control Features
- **Start/Stop Toggle**: Main automation control with confirmation dialogs
- **Pause/Resume**: Temporary suspension of automation without losing session data
- **Emergency Stop**: Immediate termination with safety checks
- **Keyboard Shortcuts**: Quick access to all major functions

### Safety & Monitoring
- **Boundary Checks**: Automatic Y-coordinate validation to prevent unsafe operations
- **Position Tracking**: Real-time player position monitoring
- **Error Handling**: Comprehensive exception handling with user feedback
- **Audio Alerts**: Sound notifications for state changes and emergencies

### Statistics & Persistence
- **Session Tracking**: Real-time statistics for current automation session
- **Historical Data**: Persistent storage of all automation sessions
- **JSON Storage**: Statistics saved to `block_automation_stats.json`
- **Runtime Calculation**: Accurate timing with pause state management

## Usage

### Starting the Script
```
\\block_automation
```

### Keyboard Shortcuts
- **Ctrl+S**: Start/Stop automation
- **Ctrl+P**: Pause/Resume automation
- **Ctrl+E**: Emergency stop
- **Ctrl+Q**: Quit application

### Configuration Options
- **Delay Range**: 0-100 milliseconds between actions (1ms precision)
- **Confirmation Dialogs**: Optional confirmation for start/stop actions
- **Audio Alerts**: Toggle sound notifications on/off
- **Safety Boundaries**: Y-coordinate limits (configurable in code)

## Safety Considerations

### Boundary Protection
- Automatic Y-coordinate checking (default: 60-320 range)
- Emergency stop if player moves outside safe boundaries
- Audio alerts for safety violations

### Emergency Features
- Immediate stop button always available
- Automatic cleanup of all active mouse actions
- Confirmation dialog when closing during active automation

### Best Practices
1. **Test in Safe Area**: Always test in a controlled environment first
2. **Monitor Boundaries**: Ensure you're in a safe Y-coordinate range
3. **Use Confirmation**: Keep confirmation dialogs enabled for safety
4. **Emergency Access**: Remember Ctrl+E for immediate emergency stop

## Technical Details

### Dependencies
- **minescript**: Core Minecraft automation library
- **customtkinter**: Modern GUI framework
- **threading**: Background automation processing
- **winsound**: Audio alert system (Windows)
- **json**: Statistics persistence

### File Structure
- `block_automation.py`: Main script file
- `block_automation_stats.json`: Statistics storage (auto-created)

### Performance
- **Threading**: Non-blocking GUI with background automation
- **Efficient Updates**: 1-second GUI refresh rate
- **Memory Management**: Automatic cleanup and resource management
- **Session Limits**: Keeps last 50 sessions to prevent file bloat

## Troubleshooting

### Common Issues
1. **"Minescript not available"**: Script must be run from within Minecraft
2. **No response**: Check if automation is paused or emergency stopped
3. **Boundary violations**: Move to appropriate Y-coordinate range
4. **Audio not working**: Check Windows sound settings

### Debug Information
- All actions logged to Minecraft chat with color coding
- Error messages displayed in GUI and console
- Statistics automatically saved on stop/emergency

## Customization

### Modifying Delays
Edit the CONFIG dictionary in the script:
```python
CONFIG = {
    "min_delay": 0,      # Minimum delay in ms
    "max_delay": 100,    # Maximum delay in ms
    "default_delay": 50, # Default delay in ms
}
```

### Adjusting Safety Boundaries
```python
"safety_boundary": {
    "min_y": 60,   # Minimum safe Y coordinate
    "max_y": 320   # Maximum safe Y coordinate
}
```

## Version Information
- **Version**: 1.0
- **Author**: Minescript Automation
- **Compatibility**: Minescript 4.0+
- **Platform**: Windows (audio alerts), Cross-platform (core functionality)

## Support
For issues or questions, check the Minescript documentation or community forums.
