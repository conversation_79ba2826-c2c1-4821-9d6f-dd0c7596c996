#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Skyblock Outpost Event Countdown Timer

This application calculates and maintains a countdown to the next Skyblock Outpost event.
Events occur every 6 hours starting at 11:00 PM PST on July 29, 2025.

The countdown is written to a text file that can be read by external applications.
While CustomHUD cannot directly read external files, this creates the foundation
for integration with custom mods or server plugins.

Author: Generated for CustomHUD Integration
Date: 2025-07-30
"""

import datetime
import pytz
import time
import os
import sys
from pathlib import Path

# Set UTF-8 encoding for stdout to handle Unicode characters
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

# Configuration
# Default CustomHUD profile path - modify this to match your Minecraft installation
DEFAULT_CUSTOMHUD_PATH = r"C:\Users\<USER>\AppData\Roaming\.minecraft\config\custom-hud\profiles\timer.txt"
# Fallback to local directory if CustomHUD directory doesn't exist
LOCAL_FALLBACK_PATH = "timer.txt"

OUTPUT_FILE = DEFAULT_CUSTOMHUD_PATH
UPDATE_INTERVAL = 60  # seconds
EVENT_INTERVAL_HOURS = 6

# Event schedules
# Skyblock events start July 29, 2025 at 11:00 PM PST
SKYBLOCK_OUTPOST_START = datetime.datetime(2025, 7, 29, 23, 0, 0)  # 11:00 PM
SKYBLOCK_POND_START = datetime.datetime(2025, 7, 30, 2, 0, 0)     # 2:00 AM (3 hours after outpost)

# Lifesteal events start July 30, 2025 at 2:00 AM PST
LIFESTEAL_OUTPOST_START = datetime.datetime(2025, 7, 30, 2, 0, 0)  # 2:00 AM
LIFESTEAL_POND_START = datetime.datetime(2025, 7, 30, 1, 0, 0)     # 1:00 AM (1 hour before outpost)
LIFESTEAL_CORE_START = datetime.datetime(2025, 7, 30, 23, 30, 0)   # 11:30 PM

PST_TIMEZONE = pytz.timezone('US/Pacific')

def safe_emoji(emoji):
    """
    Return a safe version of emoji that won't cause encoding errors.

    Args:
        emoji: Unicode emoji string

    Returns:
        str: Original emoji or ASCII fallback
    """
    try:
        # Test if the emoji can be encoded
        emoji.encode('utf-8')
        return emoji
    except UnicodeEncodeError:
        # Return ASCII alternatives for common emojis
        emoji_map = {
            '⚔ ': '[SWORD] ',
            '🎣 ': '[FISH] ',
            '⚡ ': '[BOLT] ',
            '⚡': '[BOLT]',
            '⚔': '[SWORD]',
            '🎣': '[FISH]'
        }
        return emoji_map.get(emoji, '[?] ')

def get_next_event_time(current_time_pst, start_date, interval_hours):
    """
    Calculate the next event time based on a recurring schedule.

    Args:
        current_time_pst: Current time in PST timezone
        start_date: Event start date (datetime object)
        interval_hours: Hours between events

    Returns:
        datetime: Next event time in PST
    """
    # Localize the start date to PST
    start_time_pst = PST_TIMEZONE.localize(start_date)

    # Calculate how much time has passed since the start
    time_since_start = current_time_pst - start_time_pst

    # If we're before the start date, return the start date
    if time_since_start.total_seconds() < 0:
        return start_time_pst

    # Calculate how many complete intervals have passed
    interval_seconds = interval_hours * 3600
    intervals_passed = int(time_since_start.total_seconds() // interval_seconds)

    # Calculate the next event time
    next_event = start_time_pst + datetime.timedelta(seconds=(intervals_passed + 1) * interval_seconds)

    return next_event

def get_skyblock_outpost_next(current_time_pst):
    """Get next Skyblock Outpost event (every 6 hours)."""
    return get_next_event_time(current_time_pst, SKYBLOCK_OUTPOST_START, 6)

def get_skyblock_pond_next(current_time_pst):
    """Get next Skyblock Pond event (every 6 hours, 3 hours offset from Outpost)."""
    return get_next_event_time(current_time_pst, SKYBLOCK_POND_START, 6)

def get_lifesteal_outpost_next(current_time_pst):
    """Get next Lifesteal Outpost event (every 3 hours)."""
    return get_next_event_time(current_time_pst, LIFESTEAL_OUTPOST_START, 3)

def get_lifesteal_pond_next(current_time_pst):
    """Get next Lifesteal Pond event (every 3 hours, 1 hour before Outpost)."""
    return get_next_event_time(current_time_pst, LIFESTEAL_POND_START, 3)

def get_lifesteal_core_next(current_time_pst):
    """Get next Lifesteal Core event (every 6 hours)."""
    return get_next_event_time(current_time_pst, LIFESTEAL_CORE_START, 6)

def get_dynamic_color(time_remaining):
    """
    Get the appropriate color code based on time remaining.

    Args:
        time_remaining: timedelta object representing time until next event

    Returns:
        str: CustomHUD color code
    """
    total_seconds = int(time_remaining.total_seconds())

    if total_seconds <= 0:
        return "&a"  # Green for event starting
    elif total_seconds <= 300:  # 5 minutes or less
        return "&a"  # Green for imminent events
    elif total_seconds >= (6 * 3600 - 600):  # Within 10 minutes of reset (for 6-hour events)
        return "&6"  # Orange for recently reset events
    elif total_seconds >= (3 * 3600 - 600):  # Within 10 minutes of reset (for 3-hour events)
        return "&6"  # Orange for recently reset events
    else:
        return "&7"  # Gray for normal countdown

def format_countdown_with_color(time_remaining, event_name, emoji=""):
    """
    Format the time remaining with dynamic colors and emojis.

    Args:
        time_remaining: timedelta object representing time until next event
        event_name: Name of the event (e.g., "Outpost", "Pond")
        emoji: Optional emoji to display

    Returns:
        str: Formatted countdown string with colors
    """
    total_seconds = int(time_remaining.total_seconds())
    color = get_dynamic_color(time_remaining)
    safe_emoji_str = safe_emoji(emoji)

    if total_seconds <= 0:
        return f"{color}{safe_emoji_str}{event_name}: &l&aEVENT STARTING!"

    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60

    # Handle singular vs plural
    hour_text = "h" if hours == 1 else "h"
    minute_text = "m" if minutes == 1 else "m"

    if hours > 0 and minutes > 0:
        return f"{color}{safe_emoji_str}{event_name}: {hours}{hour_text} {minutes}{minute_text}"
    elif hours > 0:
        return f"{color}{safe_emoji_str}{event_name}: {hours}{hour_text}"
    elif minutes > 0:
        return f"{color}{safe_emoji_str}{event_name}: {minutes}{minute_text}"
    else:
        return f"{color}{safe_emoji_str}{event_name}: &l<1m"

def create_customhud_profile(countdown_data):
    """
    Create a complete CustomHUD profile configuration with multi-server countdowns.

    Args:
        countdown_data: Dictionary containing formatted countdown strings for all events

    Returns:
        str: Complete CustomHUD profile configuration
    """
    # Use safe emojis for the profile headers
    skyblock_emoji = safe_emoji('⚔')
    lifesteal_emoji = safe_emoji('⚡')

    profile_content = f"""==Section:TopLeft==
&6&l{skyblock_emoji} Skyblock
{countdown_data['skyblock_outpost']}
{countdown_data['skyblock_pond']}

&c&l{lifesteal_emoji} Lifesteal
{countdown_data['lifesteal_outpost']}
{countdown_data['lifesteal_pond']}
{countdown_data['lifesteal_core']}
"""
    return profile_content

def write_countdown_to_file(countdown_data):
    """
    Write the countdown data as a complete CustomHUD profile with error handling.
    Falls back to local directory if CustomHUD directory is not accessible.

    Args:
        countdown_data: Dictionary containing formatted countdown strings for all events
    """
    global OUTPUT_FILE

    # Validate that all required keys are present
    required_keys = ['skyblock_outpost', 'skyblock_pond', 'lifesteal_outpost', 'lifesteal_pond', 'lifesteal_core']
    for key in required_keys:
        if key not in countdown_data:
            countdown_data[key] = "&8Missing"

    try:
        # Ensure the directory exists
        profile_dir = os.path.dirname(OUTPUT_FILE)
        os.makedirs(profile_dir, exist_ok=True)

        # Create the complete CustomHUD profile
        profile_content = create_customhud_profile(countdown_data)

        # Atomic write: write to temporary file first, then rename
        temp_file = OUTPUT_FILE + '.tmp'
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(profile_content)
            f.flush()  # Ensure data is written immediately
            os.fsync(f.fileno())  # Force write to disk

        # Atomic rename
        os.replace(temp_file, OUTPUT_FILE)

    except (PermissionError, FileNotFoundError, OSError):
        # Fall back to local directory if CustomHUD directory is not accessible
        if OUTPUT_FILE != LOCAL_FALLBACK_PATH:
            OUTPUT_FILE = LOCAL_FALLBACK_PATH

            try:
                profile_content = create_customhud_profile(countdown_data)
                temp_file = OUTPUT_FILE + '.tmp'
                with open(temp_file, 'w', encoding='utf-8') as f:
                    f.write(profile_content)
                    f.flush()
                    os.fsync(f.fileno())
                os.replace(temp_file, OUTPUT_FILE)
            except Exception:
                pass  # Silent fallback failure
        else:
            pass  # Silent write failure
    except Exception:
        pass  # Silent unexpected error

def calculate_all_countdowns():
    """
    Main function to calculate all countdowns and update the output file.
    """
    try:
        # Get current time in PST
        pst = pytz.timezone('US/Pacific')
        now_utc = datetime.datetime.now(pytz.UTC)
        now_pst = now_utc.astimezone(pst)

        # Initialize countdown_data with default values
        countdown_data = {
            'skyblock_outpost': "&8Error",
            'skyblock_pond': "&8Error",
            'lifesteal_outpost': "&8Error",
            'lifesteal_pond': "&8Error",
            'lifesteal_core': "&8Error"
        }

        # Calculate each event individually with error handling
        try:
            skyblock_outpost_next = get_skyblock_outpost_next(now_pst)
            skyblock_outpost_remaining = skyblock_outpost_next - now_pst
            countdown_data['skyblock_outpost'] = format_countdown_with_color(skyblock_outpost_remaining, "Outpost", "⚔ ")
        except:
            pass

        try:
            skyblock_pond_next = get_skyblock_pond_next(now_pst)
            skyblock_pond_remaining = skyblock_pond_next - now_pst
            countdown_data['skyblock_pond'] = format_countdown_with_color(skyblock_pond_remaining, "Pond", "🎣 ")
        except:
            pass

        try:
            lifesteal_outpost_next = get_lifesteal_outpost_next(now_pst)
            lifesteal_outpost_remaining = lifesteal_outpost_next - now_pst
            countdown_data['lifesteal_outpost'] = format_countdown_with_color(lifesteal_outpost_remaining, "Outpost", "⚔ ")
        except:
            pass

        try:
            lifesteal_pond_next = get_lifesteal_pond_next(now_pst)
            lifesteal_pond_remaining = lifesteal_pond_next - now_pst
            countdown_data['lifesteal_pond'] = format_countdown_with_color(lifesteal_pond_remaining, "Pond", "🎣 ")
        except:
            pass

        # Core event with retry mechanism
        for retry in range(3):  # Try up to 3 times
            try:
                lifesteal_core_next = get_lifesteal_core_next(now_pst)
                lifesteal_core_remaining = lifesteal_core_next - now_pst
                countdown_data['lifesteal_core'] = format_countdown_with_color(lifesteal_core_remaining, "Core", "⚡ ")
                break  # Success, exit retry loop
            except Exception:
                if retry == 2:  # Last retry failed
                    countdown_data['lifesteal_core'] = "&8Core Error"
                continue

        # Write to file
        write_countdown_to_file(countdown_data)

    except pytz.exceptions.UnknownTimeZoneError:
        pass  # Silent timezone error
    except Exception:
        pass  # Silent calculation error

# Keep the old function name for backward compatibility
def calculate_outpost_countdown():
    """Backward compatibility wrapper."""
    calculate_all_countdowns()

def main():
    """Main application entry point."""
    # Initial countdown calculation
    calculate_all_countdowns()

    try:
        # Main loop
        while True:
            time.sleep(UPDATE_INTERVAL)
            calculate_all_countdowns()

    except KeyboardInterrupt:
        pass  # Silent exit on Ctrl+C
    except Exception:
        pass  # Silent error handling
    finally:
        # Write final message to profile
        try:
            final_data = {
                'skyblock_outpost': "&8Timer Stopped",
                'skyblock_pond': "&8Timer Stopped",
                'lifesteal_outpost': "&8Timer Stopped",
                'lifesteal_pond': "&8Timer Stopped",
                'lifesteal_core': "&8Timer Stopped"
            }
            write_countdown_to_file(final_data)
        except:
            pass

if __name__ == "__main__":
    main()
