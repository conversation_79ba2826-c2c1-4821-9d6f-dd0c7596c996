#!/usr/bin/env python3
"""
Block Placement/Breaking Automation Script for Minescript
========================================================

Automatically alternates between placing and breaking blocks in a continuous cycle.
Features a modern CustomTkinter GUI with speed controls and safety mechanisms.

Usage: \\block_automation

Features:
- Automated block placement and breaking cycle
- Configurable delay between actions (50-2000ms)
- Modern CustomTkinter GUI with real-time controls
- Safety mechanisms and emergency stop
- Statistics tracking and session management
- Keyboard shortcuts and confirmation dialogs
- Audio alerts for state changes

Author: Minescript Automation
Version: 1.0
"""

import time
import threading
import sys
import json
import os
import winsound
from datetime import datetime, timedelta
from tkinter import messagebox
import customtkinter as ctk

# Import Minescript functions
try:
    import minescript
    from minescript import (
        player_press_use,
        player_press_attack,
        player_position,
        player_get_targeted_block,
        getblock,
        echo,
        execute
    )
    MINESCRIPT_AVAILABLE = True
except ImportError:
    MINESCRIPT_AVAILABLE = False
    echo = print
    def player_press_use(state): pass
    def player_press_attack(state): pass
    def player_position(): return [0, 0, 0]
    def player_get_targeted_block(max_distance=5): return None
    def getblock(x, y, z): return "minecraft:air"
    def execute(command): pass

# Global state management
STATE = {
    "running": False,
    "paused": False,
    "emergency_stop": False,
    "automation_thread": None,
    "start_time": None,
    "pause_time": None,
    "total_pause_duration": timedelta(),
    "blocks_placed": 0,
    "blocks_broken": 0,
    "cycles_completed": 0,
    "current_action": "Idle",
    "delay_ms": 500,  # Default delay in milliseconds
    "last_position": [0, 0, 0]
}

# Configuration
CONFIG = {
    "min_delay": 50,
    "max_delay": 2000,
    "default_delay": 500,
    "safety_boundary": {
        "min_y": 60,
        "max_y": 320
    },
    "confirm_actions": True,
    "audio_alerts": True
}

# Statistics file path
STATS_FILE = "block_automation_stats.json"

def load_stats():
    """Load statistics from file."""
    try:
        if os.path.exists(STATS_FILE):
            with open(STATS_FILE, 'r') as f:
                return json.load(f)
    except Exception as e:
        echo(f"§c[BlockAuto] Error loading stats: {e}")
    
    return {
        "total_blocks_placed": 0,
        "total_blocks_broken": 0,
        "total_cycles": 0,
        "total_runtime": 0,
        "sessions": []
    }

def save_stats():
    """Save statistics to file."""
    try:
        stats = load_stats()
        
        # Update totals
        stats["total_blocks_placed"] += STATE["blocks_placed"]
        stats["total_blocks_broken"] += STATE["blocks_broken"]
        stats["total_cycles"] += STATE["cycles_completed"]
        
        # Calculate session runtime
        if STATE["start_time"]:
            runtime = (datetime.now() - STATE["start_time"] - STATE["total_pause_duration"]).total_seconds()
            stats["total_runtime"] += runtime
            
            # Add session data
            session = {
                "date": STATE["start_time"].isoformat(),
                "runtime": runtime,
                "blocks_placed": STATE["blocks_placed"],
                "blocks_broken": STATE["blocks_broken"],
                "cycles": STATE["cycles_completed"]
            }
            stats["sessions"].append(session)
            
            # Keep only last 50 sessions
            if len(stats["sessions"]) > 50:
                stats["sessions"] = stats["sessions"][-50:]
        
        with open(STATS_FILE, 'w') as f:
            json.dump(stats, f, indent=2)
            
    except Exception as e:
        echo(f"§c[BlockAuto] Error saving stats: {e}")

def play_alert(frequency=1000, duration=200):
    """Play audio alert if enabled."""
    if CONFIG["audio_alerts"] and MINESCRIPT_AVAILABLE:
        try:
            winsound.Beep(frequency, duration)
        except:
            pass

def check_safety_boundaries():
    """Check if player is within safe boundaries."""
    try:
        pos = player_position()
        y = pos[1]
        
        if y < CONFIG["safety_boundary"]["min_y"] or y > CONFIG["safety_boundary"]["max_y"]:
            echo(f"§c[BlockAuto] Safety boundary violation! Y={y}")
            return False
            
        return True
    except Exception as e:
        echo(f"§c[BlockAuto] Error checking boundaries: {e}")
        return False

def automation_loop():
    """Main automation loop that alternates between placing and breaking blocks."""
    echo("§a[BlockAuto] Starting block automation...")
    play_alert(800, 300)
    
    try:
        while STATE["running"] and not STATE["emergency_stop"]:
            # Check pause state
            while STATE["paused"] and STATE["running"]:
                time.sleep(0.1)
                
            if not STATE["running"] or STATE["emergency_stop"]:
                break
                
            # Safety check
            if not check_safety_boundaries():
                STATE["emergency_stop"] = True
                echo("§c[BlockAuto] Emergency stop due to safety boundary violation!")
                play_alert(2000, 1000)
                break
            
            # Update current position
            STATE["last_position"] = player_position()
            
            # Place block (right-click)
            STATE["current_action"] = "Placing Block"
            player_press_use(True)
            time.sleep(0.05)  # Brief press
            player_press_use(False)
            STATE["blocks_placed"] += 1
            
            # Wait for configured delay
            delay_seconds = STATE["delay_ms"] / 1000.0
            time.sleep(delay_seconds)
            
            if not STATE["running"] or STATE["emergency_stop"]:
                break
                
            # Break block (left-click)
            STATE["current_action"] = "Breaking Block"
            player_press_attack(True)
            time.sleep(0.05)  # Brief press
            player_press_attack(False)
            STATE["blocks_broken"] += 1
            
            # Complete cycle
            STATE["cycles_completed"] += 1
            
            # Wait for configured delay
            time.sleep(delay_seconds)
            
    except Exception as e:
        echo(f"§c[BlockAuto] Error in automation loop: {e}")
        STATE["emergency_stop"] = True
    finally:
        # Ensure all actions are stopped
        player_press_use(False)
        player_press_attack(False)
        STATE["current_action"] = "Stopped"
        echo("§c[BlockAuto] Block automation stopped")
        play_alert(400, 500)

class BlockAutomationGUI(ctk.CTk):
    """Main GUI class for the block automation script."""

    def __init__(self):
        super().__init__()

        self.title("🧱 Block Automation Bot")
        self.geometry("600x800")
        self.resizable(True, True)

        # Configure main window grid weights for responsive design
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # Load statistics
        self.stats = load_stats()

        self.setup_ui()
        self.setup_keyboard_shortcuts()
        self.update_stats_loop()

        # Handle window close
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_ui(self):
        """Set up the user interface."""
        # Create main container frame that fills the window
        main_container = ctk.CTkFrame(self, fg_color="transparent")
        main_container.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        main_container.grid_columnconfigure(0, weight=1)
        main_container.grid_rowconfigure(1, weight=1)

        # Title at the top
        title_label = ctk.CTkLabel(
            main_container,
            text="🧱 Block Automation Bot",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, pady=(0, 20), sticky="ew")

        # Create scrollable frame for content
        self.main_frame = ctk.CTkScrollableFrame(main_container, fg_color="transparent")
        self.main_frame.grid(row=1, column=0, sticky="nsew")
        self.main_frame.grid_columnconfigure(0, weight=1)

        # Title
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="🧱 Block Automation Bot",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, pady=(0, 20), sticky="ew")

        # Status card
        self.setup_status_card(1)
        
        # Control card
        self.setup_control_card(2)
        
        # Speed control card
        self.setup_speed_card(3)
        
        # Statistics card
        self.setup_statistics_card(4)
        
        # Settings card
        self.setup_settings_card(5)

    def setup_status_card(self, row):
        """Setup status display card."""
        card = ctk.CTkFrame(self.main_frame)
        card.grid(row=row, column=0, sticky="ew", padx=5, pady=5)
        card.grid_columnconfigure((0, 1), weight=1)

        # Card title
        title = ctk.CTkLabel(
            card,
            text="📊 Status",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title.grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10), sticky="w")

        # Status indicator
        self.status_label = ctk.CTkLabel(
            card,
            text="Status: Stopped",
            font=ctk.CTkFont(size=14)
        )
        self.status_label.grid(row=1, column=0, columnspan=2, padx=15, pady=5, sticky="w")

        # Current action
        self.action_label = ctk.CTkLabel(
            card,
            text="Action: Idle",
            font=ctk.CTkFont(size=14)
        )
        self.action_label.grid(row=2, column=0, columnspan=2, padx=15, pady=5, sticky="w")

        # Runtime
        self.runtime_label = ctk.CTkLabel(
            card,
            text="Runtime: 00:00:00",
            font=ctk.CTkFont(size=14)
        )
        self.runtime_label.grid(row=3, column=0, columnspan=2, padx=15, pady=(5, 15), sticky="w")

    def setup_control_card(self, row):
        """Setup control buttons card."""
        card = ctk.CTkFrame(self.main_frame)
        card.grid(row=row, column=0, sticky="ew", padx=5, pady=5)
        card.grid_columnconfigure((0, 1), weight=1)

        # Card title
        title = ctk.CTkLabel(
            card,
            text="🎮 Controls",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title.grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10), sticky="w")

        # Start/Stop button
        self.start_stop_btn = ctk.CTkButton(
            card,
            text="Start Automation",
            command=self.toggle_automation,
            font=ctk.CTkFont(size=16, weight="bold"),
            height=40,
            fg_color="green",
            hover_color="darkgreen"
        )
        self.start_stop_btn.grid(row=1, column=0, columnspan=2, padx=15, pady=10, sticky="ew")

        # Pause/Resume button
        self.pause_resume_btn = ctk.CTkButton(
            card,
            text="Pause",
            command=self.toggle_pause,
            font=ctk.CTkFont(size=14),
            height=35,
            state="disabled"
        )
        self.pause_resume_btn.grid(row=2, column=0, padx=(15, 5), pady=5, sticky="ew")

        # Emergency Stop button
        emergency_btn = ctk.CTkButton(
            card,
            text="Emergency Stop",
            command=self.emergency_stop,
            font=ctk.CTkFont(size=14),
            height=35,
            fg_color="red",
            hover_color="darkred"
        )
        emergency_btn.grid(row=2, column=1, padx=(5, 15), pady=(5, 15), sticky="ew")

    def setup_speed_card(self, row):
        """Setup speed control card."""
        card = ctk.CTkFrame(self.main_frame)
        card.grid(row=row, column=0, sticky="ew", padx=5, pady=5)
        card.grid_columnconfigure(1, weight=1)

        # Card title
        title = ctk.CTkLabel(
            card,
            text="⚡ Speed Control",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title.grid(row=0, column=0, columnspan=3, padx=15, pady=(15, 10), sticky="w")

        # Speed label
        speed_label = ctk.CTkLabel(
            card,
            text="Delay (ms):",
            font=ctk.CTkFont(size=14)
        )
        speed_label.grid(row=1, column=0, padx=(15, 5), pady=5, sticky="w")

        # Speed slider
        self.speed_slider = ctk.CTkSlider(
            card,
            from_=CONFIG["min_delay"],
            to=CONFIG["max_delay"],
            number_of_steps=39,  # 50ms increments
            command=self.update_speed
        )
        self.speed_slider.set(CONFIG["default_delay"])
        self.speed_slider.grid(row=1, column=1, padx=5, pady=5, sticky="ew")

        # Speed value label
        self.speed_value_label = ctk.CTkLabel(
            card,
            text=f"{CONFIG['default_delay']}ms",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.speed_value_label.grid(row=1, column=2, padx=(5, 15), pady=(5, 15), sticky="e")

    def setup_statistics_card(self, row):
        """Setup statistics display card."""
        card = ctk.CTkFrame(self.main_frame)
        card.grid(row=row, column=0, sticky="ew", padx=5, pady=5)
        card.grid_columnconfigure((0, 1), weight=1)

        # Card title
        title = ctk.CTkLabel(
            card,
            text="📈 Statistics",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title.grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10), sticky="w")

        # Session stats
        session_frame = ctk.CTkFrame(card, fg_color="transparent")
        session_frame.grid(row=1, column=0, padx=(15, 5), pady=5, sticky="ew")

        ctk.CTkLabel(session_frame, text="Session Stats:", font=ctk.CTkFont(size=12, weight="bold")).pack(anchor="w")

        self.session_placed_label = ctk.CTkLabel(session_frame, text="Blocks Placed: 0", font=ctk.CTkFont(size=11))
        self.session_placed_label.pack(anchor="w")

        self.session_broken_label = ctk.CTkLabel(session_frame, text="Blocks Broken: 0", font=ctk.CTkFont(size=11))
        self.session_broken_label.pack(anchor="w")

        self.session_cycles_label = ctk.CTkLabel(session_frame, text="Cycles: 0", font=ctk.CTkFont(size=11))
        self.session_cycles_label.pack(anchor="w")

        # Total stats
        total_frame = ctk.CTkFrame(card, fg_color="transparent")
        total_frame.grid(row=1, column=1, padx=(5, 15), pady=(5, 15), sticky="ew")

        ctk.CTkLabel(total_frame, text="Total Stats:", font=ctk.CTkFont(size=12, weight="bold")).pack(anchor="w")

        self.total_placed_label = ctk.CTkLabel(total_frame, text=f"Blocks Placed: {self.stats['total_blocks_placed']}", font=ctk.CTkFont(size=11))
        self.total_placed_label.pack(anchor="w")

        self.total_broken_label = ctk.CTkLabel(total_frame, text=f"Blocks Broken: {self.stats['total_blocks_broken']}", font=ctk.CTkFont(size=11))
        self.total_broken_label.pack(anchor="w")

        self.total_cycles_label = ctk.CTkLabel(total_frame, text=f"Total Cycles: {self.stats['total_cycles']}", font=ctk.CTkFont(size=11))
        self.total_cycles_label.pack(anchor="w")

    def setup_settings_card(self, row):
        """Setup settings card."""
        card = ctk.CTkFrame(self.main_frame)
        card.grid(row=row, column=0, sticky="ew", padx=5, pady=5)
        card.grid_columnconfigure((0, 1), weight=1)

        # Card title
        title = ctk.CTkLabel(
            card,
            text="⚙️ Settings",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title.grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10), sticky="w")

        # Confirmation checkbox
        self.confirm_var = ctk.BooleanVar(value=CONFIG["confirm_actions"])
        confirm_checkbox = ctk.CTkCheckBox(
            card,
            text="Confirm actions",
            variable=self.confirm_var,
            font=ctk.CTkFont(size=12)
        )
        confirm_checkbox.grid(row=1, column=0, padx=15, pady=5, sticky="w")

        # Audio alerts checkbox
        self.audio_var = ctk.BooleanVar(value=CONFIG["audio_alerts"])
        audio_checkbox = ctk.CTkCheckBox(
            card,
            text="Audio alerts",
            variable=self.audio_var,
            command=self.update_audio_setting,
            font=ctk.CTkFont(size=12)
        )
        audio_checkbox.grid(row=1, column=1, padx=15, pady=(5, 15), sticky="w")

    def setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts."""
        self.bind("<Control-s>", lambda e: self.toggle_automation())
        self.bind("<Control-p>", lambda e: self.toggle_pause())
        self.bind("<Control-e>", lambda e: self.emergency_stop())
        self.bind("<Control-q>", lambda e: self.on_closing())

    def update_speed(self, value):
        """Update automation speed."""
        delay = int(value)
        STATE["delay_ms"] = delay
        self.speed_value_label.configure(text=f"{delay}ms")

    def update_audio_setting(self):
        """Update audio alert setting."""
        CONFIG["audio_alerts"] = self.audio_var.get()

    def toggle_automation(self):
        """Toggle automation on/off with confirmation if enabled."""
        if not STATE["running"]:
            if self.confirm_var.get():
                if not messagebox.askyesno("Confirm", "Start block automation?\n\nThis will continuously place and break blocks."):
                    return
            self.start_automation()
        else:
            if self.confirm_var.get():
                if not messagebox.askyesno("Confirm", "Stop block automation?"):
                    return
            self.stop_automation()

    def start_automation(self):
        """Start the automation process."""
        if not MINESCRIPT_AVAILABLE:
            messagebox.showerror("Error", "Minescript not available! This script must be run from within Minecraft.")
            return

        STATE["running"] = True
        STATE["paused"] = False
        STATE["emergency_stop"] = False
        STATE["start_time"] = datetime.now()
        STATE["total_pause_duration"] = timedelta()
        STATE["blocks_placed"] = 0
        STATE["blocks_broken"] = 0
        STATE["cycles_completed"] = 0

        # Start automation thread
        STATE["automation_thread"] = threading.Thread(target=automation_loop, daemon=True)
        STATE["automation_thread"].start()

        # Update UI
        self.start_stop_btn.configure(text="Stop Automation", fg_color="red", hover_color="darkred")
        self.pause_resume_btn.configure(state="normal")

        echo("§a[BlockAuto] Block automation started!")

    def stop_automation(self):
        """Stop the automation process."""
        STATE["running"] = False
        STATE["paused"] = False

        # Save statistics
        save_stats()

        # Update UI
        self.start_stop_btn.configure(text="Start Automation", fg_color="green", hover_color="darkgreen")
        self.pause_resume_btn.configure(text="Pause", state="disabled")

        echo("§c[BlockAuto] Block automation stopped!")

    def toggle_pause(self):
        """Toggle pause/resume."""
        if STATE["paused"]:
            # Resume
            STATE["paused"] = False
            if STATE["pause_time"]:
                STATE["total_pause_duration"] += datetime.now() - STATE["pause_time"]
                STATE["pause_time"] = None
            self.pause_resume_btn.configure(text="Pause")
            echo("§a[BlockAuto] Automation resumed")
        else:
            # Pause
            STATE["paused"] = True
            STATE["pause_time"] = datetime.now()
            self.pause_resume_btn.configure(text="Resume")
            echo("§e[BlockAuto] Automation paused")

    def emergency_stop(self):
        """Emergency stop all operations."""
        STATE["emergency_stop"] = True
        STATE["running"] = False
        STATE["paused"] = False

        # Ensure all actions are stopped
        if MINESCRIPT_AVAILABLE:
            player_press_use(False)
            player_press_attack(False)

        # Save statistics
        save_stats()

        # Update UI
        self.start_stop_btn.configure(text="Start Automation", fg_color="green", hover_color="darkgreen")
        self.pause_resume_btn.configure(text="Pause", state="disabled")

        echo("§c[BlockAuto] EMERGENCY STOP ACTIVATED!")
        play_alert(2000, 1000)

    def update_stats_loop(self):
        """Update statistics display in a loop."""
        try:
            # Update status
            if STATE["running"]:
                if STATE["paused"]:
                    status_text = "Status: Paused"
                    status_color = "orange"
                else:
                    status_text = "Status: Running"
                    status_color = "green"
            else:
                status_text = "Status: Stopped"
                status_color = "red"

            self.status_label.configure(text=status_text, text_color=status_color)

            # Update current action
            self.action_label.configure(text=f"Action: {STATE['current_action']}")

            # Update runtime
            if STATE["start_time"]:
                if STATE["paused"] and STATE["pause_time"]:
                    runtime = (STATE["pause_time"] - STATE["start_time"] - STATE["total_pause_duration"])
                else:
                    runtime = (datetime.now() - STATE["start_time"] - STATE["total_pause_duration"])

                hours, remainder = divmod(int(runtime.total_seconds()), 3600)
                minutes, seconds = divmod(remainder, 60)
                runtime_text = f"Runtime: {hours:02d}:{minutes:02d}:{seconds:02d}"
            else:
                runtime_text = "Runtime: 00:00:00"

            self.runtime_label.configure(text=runtime_text)

            # Update session statistics
            self.session_placed_label.configure(text=f"Blocks Placed: {STATE['blocks_placed']}")
            self.session_broken_label.configure(text=f"Blocks Broken: {STATE['blocks_broken']}")
            self.session_cycles_label.configure(text=f"Cycles: {STATE['cycles_completed']}")

        except Exception as e:
            echo(f"§c[BlockAuto] Error updating stats: {e}")

        # Schedule next update
        self.after(1000, self.update_stats_loop)

    def on_closing(self):
        """Handle window closing."""
        if STATE["running"]:
            if messagebox.askyesno("Confirm", "Automation is still running. Stop and exit?"):
                self.emergency_stop()
                self.destroy()
        else:
            self.destroy()

def main():
    """Main function to run the block automation GUI."""
    try:
        # Set CustomTkinter appearance
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")

        # Create and run GUI
        app = BlockAutomationGUI()
        app.mainloop()

    except Exception as e:
        echo(f"§c[BlockAuto] Failed to start GUI: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
