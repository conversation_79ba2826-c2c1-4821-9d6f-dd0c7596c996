#!/usr/bin/env python3
"""
Advanced Mining Automation Script v2.0
Modern, feature-rich mining bot with comprehensive GUI and safety features.

Features:
- Modern scrollable GUI with organized sections
- Profile-based configuration system
- Advanced movement patterns with human-like behavior
- Comprehensive statistics tracking
- Enhanced safety features and boundary detection
- Robust auto-selling system with configurable triggers
- Session persistence and recovery
- Real-time status monitoring
"""

import time
import customtkinter as ctk
import threading
import random
import math
import os
import sys
import json
import traceback
import queue
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum

# Minescript imports
from minescript import (
    player_press_forward,
    player_press_attack,
    player_press_left,
    player_press_right,
    player_press_jump,
    player_set_orientation,
    player_position,
    player_get_targeted_block,
    player_orientation,
    player_inventory,
    getblock,
    chat,
    echo,
    execute
)

# Constants
VERSION = "2.0"
CONFIG_FILE = "mining_config.json"
STATS_FILE = "mining_stats.json"
PROFILES_FILE = "mining_profiles.json"
SESSION_FILE = "mining_session.json"

# Enums for better type safety
class MiningState(Enum):
    STOPPED = "stopped"
    STARTING = "starting"
    MINING = "mining"
    RESETTING = "resetting"
    SELLING = "selling"
    PAUSED = "paused"
    ERROR = "error"

class MovementPattern(Enum):
    CONSERVATIVE = "conservative"
    AGGRESSIVE = "aggressive"
    RANDOM = "random"
    HUMAN_LIKE = "human_like"

class SellTrigger(Enum):
    SLOT_BASED = "slot_based"
    PERCENTAGE = "percentage"
    ITEM_TYPE = "item_type"
    TIME_BASED = "time_based"

# Global state management
class MiningState_Manager:
    """Thread-safe state management for the mining bot."""

    def __init__(self):
        self._lock = threading.RLock()
        self._state = MiningState.STOPPED
        self._current_action = "Idle"
        self._error_message = ""
        self._stats_queue = queue.Queue()

    def get_state(self) -> MiningState:
        with self._lock:
            return self._state

    def set_state(self, state: MiningState, action: str = ""):
        with self._lock:
            self._state = state
            if action:
                self._current_action = action

    def get_current_action(self) -> str:
        with self._lock:
            return self._current_action

    def set_error(self, message: str):
        with self._lock:
            self._error_message = message
            self._state = MiningState.ERROR

    def get_error(self) -> str:
        with self._lock:
            return self._error_message

    def is_running(self) -> bool:
        with self._lock:
            return self._state in [MiningState.MINING, MiningState.RESETTING, MiningState.SELLING]

# Initialize global state manager
STATE_MANAGER = MiningState_Manager()


# Data classes for configuration
@dataclass
class MovementConfig:
    """Configuration for movement patterns and behavior."""
    pattern: MovementPattern = MovementPattern.HUMAN_LIKE
    speed: float = 0.005
    steps: int = 10
    randomization: float = 0.2
    micro_movements: bool = True
    anti_detection_delay: Tuple[float, float] = (0.05, 0.15)
    cursor_smoothness: float = 0.8

@dataclass
class SafetyConfig:
    """Safety and boundary configuration."""
    enable_boundaries: bool = True
    boundary_x: Tuple[int, int] = (-100, 100)
    boundary_y: Tuple[int, int] = (0, 255)
    boundary_z: Tuple[int, int] = (-100, 100)
    emergency_stop_key: str = "F1"
    stuck_detection_time: float = 3.0
    enable_audio_alarms: bool = True

@dataclass
class SellingConfig:
    """Auto-selling system configuration."""
    enabled: bool = True
    trigger_type: SellTrigger = SellTrigger.SLOT_BASED
    trigger_slots: List[int] = None
    trigger_percentage: float = 90.0
    trigger_items: List[str] = None
    cooldown_seconds: float = 5.0
    retry_attempts: int = 3
    commands: List[str] = None

    def __post_init__(self):
        if self.trigger_slots is None:
            self.trigger_slots = [35]
        if self.trigger_items is None:
            self.trigger_items = []
        if self.commands is None:
            self.commands = ["/sellall"]

@dataclass
class ResetConfig:
    """Mine reset handling configuration."""
    enabled: bool = True
    detection_blocks: List[str] = None
    reset_commands: List[str] = None
    reset_delay: float = 5.0
    retry_attempts: int = 3
    failure_action: str = "stop"  # stop, retry, ignore

    def __post_init__(self):
        if self.detection_blocks is None:
            self.detection_blocks = ["minecraft:bedrock"]
        if self.reset_commands is None:
            self.reset_commands = ["/mine y"]

@dataclass
class MiningProfile:
    """Complete mining profile configuration."""
    name: str = "Default"
    description: str = "Default mining profile"
    movement: MovementConfig = None
    safety: SafetyConfig = None
    selling: SellingConfig = None
    reset: ResetConfig = None
    obstacles: List[str] = None

    def __post_init__(self):
        if self.movement is None:
            self.movement = MovementConfig()
        if self.safety is None:
            self.safety = SafetyConfig()
        if self.selling is None:
            self.selling = SellingConfig()
        if self.reset is None:
            self.reset = ResetConfig()
        if self.obstacles is None:
            self.obstacles = ["minecraft:bedrock", "minecraft:ladder", "minecraft:shroomlight"]

@dataclass
class SessionStats:
    """Statistics for the current mining session."""
    start_time: datetime = None
    blocks_mined: int = 0
    resets_performed: int = 0
    items_sold: int = 0
    sell_attempts: int = 0
    sell_failures: int = 0
    runtime_seconds: float = 0.0
    distance_traveled: float = 0.0
    obstacles_encountered: int = 0

    def __post_init__(self):
        if self.start_time is None:
            self.start_time = datetime.now()

@dataclass
class OverallStats:
    """Overall statistics across all sessions."""
    total_runtime: float = 0.0
    total_blocks_mined: int = 0
    total_resets: int = 0
    total_items_sold: int = 0
    sessions_completed: int = 0
    last_session: datetime = None

    def __post_init__(self):
        if self.last_session is None:
            self.last_session = datetime.now()

# Configuration management class
class ConfigManager:
    """Manages configuration loading, saving, and validation."""

    def __init__(self):
        self.current_profile = "default"
        self.profiles: Dict[str, MiningProfile] = {}
        self.session_stats = SessionStats()
        self.overall_stats = OverallStats()
        self._load_all_configs()

    def _load_all_configs(self):
        """Load all configuration files."""
        self._load_profiles()
        self._load_stats()
        self._migrate_old_config()

    def _load_profiles(self):
        """Load mining profiles from file."""
        try:
            if os.path.exists(PROFILES_FILE):
                with open(PROFILES_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for name, profile_data in data.items():
                        self.profiles[name] = self._dict_to_profile(profile_data)
            else:
                # Create default profiles
                self._create_default_profiles()
                self.save_profiles()
        except Exception as e:
            echo(f"§c[Config] Error loading profiles: {e}")
            self._create_default_profiles()

    def _create_default_profiles(self):
        """Create default mining profiles."""
        # Conservative profile
        conservative = MiningProfile(
            name="Conservative",
            description="Safe mining with slower movements and extra safety checks",
            movement=MovementConfig(
                pattern=MovementPattern.CONSERVATIVE,
                speed=0.008,
                steps=15,
                randomization=0.1
            )
        )

        # Aggressive profile
        aggressive = MiningProfile(
            name="Aggressive",
            description="Fast mining with quick movements for maximum efficiency",
            movement=MovementConfig(
                pattern=MovementPattern.AGGRESSIVE,
                speed=0.003,
                steps=8,
                randomization=0.3
            )
        )

        # Default profile
        default = MiningProfile(
            name="Default",
            description="Balanced mining profile with human-like behavior"
        )

        self.profiles = {
            "default": default,
            "conservative": conservative,
            "aggressive": aggressive
        }

    def _dict_to_profile(self, data: Dict) -> MiningProfile:
        """Convert dictionary data to MiningProfile object."""
        try:
            # Handle nested dataclass conversion
            if 'movement' in data and isinstance(data['movement'], dict):
                data['movement'] = MovementConfig(**data['movement'])
            if 'safety' in data and isinstance(data['safety'], dict):
                data['safety'] = SafetyConfig(**data['safety'])
            if 'selling' in data and isinstance(data['selling'], dict):
                data['selling'] = SellingConfig(**data['selling'])
            if 'reset' in data and isinstance(data['reset'], dict):
                data['reset'] = ResetConfig(**data['reset'])

            return MiningProfile(**data)
        except Exception as e:
            echo(f"§c[Config] Error converting profile data: {e}")
            return MiningProfile()

    def _load_stats(self):
        """Load statistics from file."""
        try:
            if os.path.exists(STATS_FILE):
                with open(STATS_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if 'overall' in data:
                        # Convert datetime strings back to datetime objects
                        overall_data = data['overall']
                        if 'last_session' in overall_data:
                            overall_data['last_session'] = datetime.fromisoformat(overall_data['last_session'])
                        self.overall_stats = OverallStats(**overall_data)
        except Exception as e:
            echo(f"§c[Config] Error loading stats: {e}")
            self.overall_stats = OverallStats()

    def _migrate_old_config(self):
        """Migrate old prisonconfig.txt to new system."""
        old_config_file = "prisonconfig.txt"
        if os.path.exists(old_config_file):
            try:
                echo("§e[Config] Migrating old configuration...")
                with open(old_config_file, 'r') as f:
                    lines = f.readlines()
                    for line in lines:
                        if '=' in line:
                            key, value = line.strip().split('=', 1)
                            if key == "default_steps":
                                # Migrate to movement config
                                if "default" in self.profiles:
                                    self.profiles["default"].movement.steps = int(value)

                # Save migrated config and remove old file
                self.save_profiles()
                os.rename(old_config_file, f"{old_config_file}.backup")
                echo("§a[Config] Migration completed successfully")
            except Exception as e:
                echo(f"§c[Config] Error during migration: {e}")

    def save_profiles(self):
        """Save all profiles to file."""
        try:
            data = {}
            for name, profile in self.profiles.items():
                data[name] = asdict(profile)

            with open(PROFILES_FILE, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, default=str)
        except Exception as e:
            echo(f"§c[Config] Error saving profiles: {e}")

    def save_stats(self):
        """Save statistics to file."""
        try:
            data = {
                'overall': asdict(self.overall_stats),
                'session': asdict(self.session_stats)
            }

            with open(STATS_FILE, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, default=str)
        except Exception as e:
            echo(f"§c[Config] Error saving stats: {e}")

    def get_current_profile(self) -> MiningProfile:
        """Get the currently active profile."""
        return self.profiles.get(self.current_profile, self.profiles["default"])

    def set_current_profile(self, profile_name: str):
        """Set the current active profile."""
        if profile_name in self.profiles:
            self.current_profile = profile_name
            echo(f"§a[Config] Switched to profile: {profile_name}")
        else:
            echo(f"§c[Config] Profile not found: {profile_name}")

    def create_profile(self, name: str, base_profile: str = "default") -> bool:
        """Create a new profile based on an existing one."""
        if name in self.profiles:
            return False

        if base_profile in self.profiles:
            # Deep copy the base profile
            base = self.profiles[base_profile]
            new_profile = MiningProfile(
                name=name,
                description=f"Custom profile based on {base_profile}",
                movement=MovementConfig(**asdict(base.movement)),
                safety=SafetyConfig(**asdict(base.safety)),
                selling=SellingConfig(**asdict(base.selling)),
                reset=ResetConfig(**asdict(base.reset)),
                obstacles=base.obstacles.copy()
            )
            self.profiles[name] = new_profile
            self.save_profiles()
            return True
        return False

    def delete_profile(self, name: str) -> bool:
        """Delete a profile (cannot delete default)."""
        if name == "default" or name not in self.profiles:
            return False

        del self.profiles[name]
        if self.current_profile == name:
            self.current_profile = "default"
        self.save_profiles()
        return True

# Initialize global configuration manager
CONFIG_MANAGER = ConfigManager()

# Compatibility layer for old config system
class ConfigCompatibility:
    """Compatibility layer to bridge old config dict with new ConfigManager."""

    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.script_running = False
        self.restart_in_progress = False

    def __getitem__(self, key):
        if key == "script_running":
            return self.script_running
        elif key == "restart_in_progress":
            return self.restart_in_progress
        elif key == "default_steps":
            return self.config_manager.get_current_profile().movement.steps
        else:
            return None

    def __setitem__(self, key, value):
        if key == "script_running":
            self.script_running = value
        elif key == "restart_in_progress":
            self.restart_in_progress = value
        elif key == "default_steps":
            profile = self.config_manager.get_current_profile()
            profile.movement.steps = value
            self.config_manager.save_profiles()

# Create compatibility config object
config = ConfigCompatibility(CONFIG_MANAGER)

# Compatibility function for save_config
def save_config():
    """Compatibility function for old save_config calls."""
    CONFIG_MANAGER.save_profiles()
    CONFIG_MANAGER.save_stats()



class ModernMiningGUI(ctk.CTk):
    """Modern, comprehensive mining automation GUI with scrollable layout."""

    def __init__(self):
        super().__init__()

        # Initialize GUI state
        self.config_manager = CONFIG_MANAGER
        self.current_profile = self.config_manager.get_current_profile()
        self.script_thread = None
        self.update_thread = None
        self.running = False

        # Configure window
        self.title(f"🔨 Advanced Mining Bot v{VERSION}")
        self.geometry("600x800")
        self.resizable(True, True)

        # Configure grid
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # Create main scrollable frame
        self.main_frame = ctk.CTkScrollableFrame(self)
        self.main_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        self.main_frame.grid_columnconfigure(0, weight=1)

        # Initialize GUI components
        self.setup_ui()
        self.setup_status_updates()

        # Handle window close
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_ui(self):
        """Set up the complete user interface."""
        row = 0

        # Title section
        row = self.create_title_section(row)

        # Status section
        row = self.create_status_section(row)

        # Profile section
        row = self.create_profile_section(row)

        # Mining control section
        row = self.create_mining_control_section(row)

        # Movement settings section
        row = self.create_movement_section(row)

        # Safety features section
        row = self.create_safety_section(row)

        # Auto-selling section
        row = self.create_selling_section(row)

        # Mine reset section
        row = self.create_reset_section(row)

        # Statistics section
        row = self.create_statistics_section(row)

        # Chat/Command section
        row = self.create_chat_section(row)

        # Debug section
        row = self.create_debug_section(row)

    def create_title_section(self, row: int) -> int:
        """Create the title section."""
        title_frame = ctk.CTkFrame(self.main_frame)
        title_frame.grid(row=row, column=0, pady=(0, 10), sticky="ew")
        title_frame.grid_columnconfigure(0, weight=1)

        title_label = ctk.CTkLabel(
            title_frame,
            text=f"🔨 Advanced Mining Bot v{VERSION}",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, pady=10)

        subtitle_label = ctk.CTkLabel(
            title_frame,
            text="Modern mining automation with comprehensive features",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        subtitle_label.grid(row=1, column=0, pady=(0, 10))

        return row + 1

    def create_status_section(self, row: int) -> int:
        """Create the real-time status section."""
        status_frame = ctk.CTkFrame(self.main_frame)
        status_frame.grid(row=row, column=0, pady=(0, 10), sticky="ew")
        status_frame.grid_columnconfigure(1, weight=1)

        # Section title
        status_title = ctk.CTkLabel(
            status_frame,
            text="📊 System Status",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        status_title.grid(row=0, column=0, columnspan=2, pady=(10, 5), sticky="w")

        # Status indicators
        ctk.CTkLabel(status_frame, text="State:").grid(row=1, column=0, padx=(10, 5), pady=2, sticky="w")
        self.status_state_label = ctk.CTkLabel(status_frame, text="Stopped", text_color="red")
        self.status_state_label.grid(row=1, column=1, padx=(5, 10), pady=2, sticky="w")

        ctk.CTkLabel(status_frame, text="Action:").grid(row=2, column=0, padx=(10, 5), pady=2, sticky="w")
        self.status_action_label = ctk.CTkLabel(status_frame, text="Idle")
        self.status_action_label.grid(row=2, column=1, padx=(5, 10), pady=2, sticky="w")

        ctk.CTkLabel(status_frame, text="Runtime:").grid(row=3, column=0, padx=(10, 5), pady=2, sticky="w")
        self.status_runtime_label = ctk.CTkLabel(status_frame, text="00:00:00")
        self.status_runtime_label.grid(row=3, column=1, padx=(5, 10), pady=2, sticky="w")

        ctk.CTkLabel(status_frame, text="Profile:").grid(row=4, column=0, padx=(10, 5), pady=(2, 10), sticky="w")
        self.status_profile_label = ctk.CTkLabel(status_frame, text=self.current_profile.name)
        self.status_profile_label.grid(row=4, column=1, padx=(5, 10), pady=(2, 10), sticky="w")

        return row + 1

    def create_profile_section(self, row: int) -> int:
        """Create the profile selection section."""
        profile_frame = ctk.CTkFrame(self.main_frame)
        profile_frame.grid(row=row, column=0, pady=(0, 10), sticky="ew")
        profile_frame.grid_columnconfigure(1, weight=1)

        # Section title
        profile_title = ctk.CTkLabel(
            profile_frame,
            text="⚙️ Mining Profile",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        profile_title.grid(row=0, column=0, columnspan=3, pady=(10, 5), sticky="w")

        # Profile selection
        ctk.CTkLabel(profile_frame, text="Active Profile:").grid(row=1, column=0, padx=(10, 5), pady=5, sticky="w")

        self.profile_var = ctk.StringVar(value=self.config_manager.current_profile)
        self.profile_dropdown = ctk.CTkOptionMenu(
            profile_frame,
            variable=self.profile_var,
            values=list(self.config_manager.profiles.keys()),
            command=self.on_profile_change
        )
        self.profile_dropdown.grid(row=1, column=1, padx=5, pady=5, sticky="ew")

        # Profile management buttons
        self.new_profile_button = ctk.CTkButton(
            profile_frame,
            text="New",
            width=60,
            command=self.create_new_profile
        )
        self.new_profile_button.grid(row=1, column=2, padx=(5, 10), pady=5)

        # Profile description
        self.profile_desc_label = ctk.CTkLabel(
            profile_frame,
            text=self.current_profile.description,
            wraplength=500,
            justify="left"
        )
        self.profile_desc_label.grid(row=2, column=0, columnspan=3, padx=10, pady=(0, 10), sticky="ew")

        return row + 1

    def create_mining_control_section(self, row: int) -> int:
        """Create the main mining control section."""
        control_frame = ctk.CTkFrame(self.main_frame)
        control_frame.grid(row=row, column=0, pady=(0, 10), sticky="ew")
        control_frame.grid_columnconfigure(0, weight=1)

        # Section title
        control_title = ctk.CTkLabel(
            control_frame,
            text="🎮 Mining Control",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        control_title.grid(row=0, column=0, pady=(10, 10))

        # Main control buttons
        button_frame = ctk.CTkFrame(control_frame)
        button_frame.grid(row=1, column=0, padx=10, pady=(0, 10), sticky="ew")
        button_frame.grid_columnconfigure((0, 1, 2), weight=1)

        self.start_button = ctk.CTkButton(
            button_frame,
            text="▶️ Start Mining",
            command=self.start_mining,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.start_button.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

        self.pause_button = ctk.CTkButton(
            button_frame,
            text="⏸️ Pause",
            command=self.pause_mining,
            height=40,
            state="disabled"
        )
        self.pause_button.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

        self.stop_button = ctk.CTkButton(
            button_frame,
            text="⏹️ Stop",
            command=self.stop_mining,
            height=40,
            state="disabled"
        )
        self.stop_button.grid(row=0, column=2, padx=5, pady=5, sticky="ew")

        # Emergency stop
        self.emergency_button = ctk.CTkButton(
            control_frame,
            text="🚨 EMERGENCY STOP",
            command=self.emergency_stop,
            height=35,
            fg_color="red",
            hover_color="darkred"
        )
        self.emergency_button.grid(row=2, column=0, padx=10, pady=(0, 10), sticky="ew")

        return row + 1

    def create_movement_section(self, row: int) -> int:
        """Create the movement configuration section."""
        movement_frame = ctk.CTkFrame(self.main_frame)
        movement_frame.grid(row=row, column=0, pady=(0, 10), sticky="ew")
        movement_frame.grid_columnconfigure(1, weight=1)

        # Section title
        movement_title = ctk.CTkLabel(
            movement_frame,
            text="🏃 Movement Settings",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        movement_title.grid(row=0, column=0, columnspan=2, pady=(10, 10), sticky="w")

        # Movement pattern
        ctk.CTkLabel(movement_frame, text="Pattern:").grid(row=1, column=0, padx=(10, 5), pady=5, sticky="w")
        self.pattern_var = ctk.StringVar(value=self.current_profile.movement.pattern.value)
        self.pattern_dropdown = ctk.CTkOptionMenu(
            movement_frame,
            variable=self.pattern_var,
            values=[p.value for p in MovementPattern],
            command=self.on_pattern_change
        )
        self.pattern_dropdown.grid(row=1, column=1, padx=(5, 10), pady=5, sticky="ew")

        # Speed setting
        ctk.CTkLabel(movement_frame, text="Speed:").grid(row=2, column=0, padx=(10, 5), pady=5, sticky="w")
        self.speed_var = ctk.DoubleVar(value=self.current_profile.movement.speed)
        self.speed_slider = ctk.CTkSlider(
            movement_frame,
            from_=0.001,
            to=0.02,
            variable=self.speed_var,
            command=self.on_speed_change
        )
        self.speed_slider.grid(row=2, column=1, padx=(5, 10), pady=5, sticky="ew")

        # Speed label
        self.speed_label = ctk.CTkLabel(movement_frame, text=f"{self.speed_var.get():.3f}")
        self.speed_label.grid(row=3, column=1, padx=(5, 10), pady=(0, 5), sticky="w")

        # Steps setting
        ctk.CTkLabel(movement_frame, text="Steps:").grid(row=4, column=0, padx=(10, 5), pady=5, sticky="w")
        self.steps_var = ctk.IntVar(value=self.current_profile.movement.steps)
        self.steps_slider = ctk.CTkSlider(
            movement_frame,
            from_=5,
            to=30,
            number_of_steps=25,
            variable=self.steps_var,
            command=self.on_steps_change
        )
        self.steps_slider.grid(row=4, column=1, padx=(5, 10), pady=5, sticky="ew")

        # Steps label
        self.steps_label = ctk.CTkLabel(movement_frame, text=str(self.steps_var.get()))
        self.steps_label.grid(row=5, column=1, padx=(5, 10), pady=(0, 10), sticky="w")

        return row + 1

    def send_chat(self, event=None):
        """
        Sends a chat message or executes a command based on the message input.
        Clears the chat entry after sending the message.
        """
        message = self.chat_entry.get()
        if message:
            if message.startswith("/"):
                execute(message)  # Call execute for commands
                print(f"Command executed: {message}")
            else:
                chat(message)  # Call chat for normal messages
                print(f"Chat message sent: {message}")

            self.chat_entry.delete(0, 'end')  # Clear the chat entry field

    def update_speed(self, value):
        """
        Updates the speed (inverted steps) for the sequence based on the slider value.
        """
        config["default_steps"] = int(value)  # Convert slider value to integer
        self.speed_label.configure(text=f"Speed: {config['default_steps']}")
        save_config()

    def toggle_script(self):
        """
        Toggles the script on and off. Handles starting and stopping gracefully.
        """
        config["script_running"] = not config["script_running"]

        if config["script_running"]:
            self.toggle_button.configure(text="Stop Script")
            print("Script started.")
            self.start_script_thread()
        else:
            self.toggle_button.configure(text="Start Script")
            print("Stopping script.")
            config["script_running"] = False  # Signal the thread to stop
            stop_all_movements()

    def start_script_thread(self):
        """
        Starts the main mining script in a separate thread.
        Safely stops any previous thread before starting a new one.
        """
        if self.script_thread is not None and self.script_thread.is_alive():
            print("Stopping previous thread.")
            config["script_running"] = False  # Signal the thread to stop
            self.script_thread.join(timeout=1)  # Wait for the thread to finish
            if self.script_thread.is_alive():
                print("Thread did not finish in time. Forcing termination.")
                return  # Skip starting a new thread if the previous one is stuck

        # Reset the script_running flag and start a new thread
        config["script_running"] = True
        self.script_thread = threading.Thread(target=auto_mining_loop, daemon=True)
        self.script_thread.start()
        print("New thread started.")

    def exit_script(self):
        """
        Stops the entire script, including GUI and mining thread.
        """
        global terminate_script
        print("Exiting the script...")
        terminate_script = True  # Set the terminate flag
        config["script_running"] = False  # Ensure the mining loop stops
        save_config()
        self.destroy()



OBSTACLES = ["minecraft:bedrock", "minecraft:ladder", "minecraft:shroomlight"]
mouse_speed = 1.0
script_running = False
pitch_direction = -1

def check_for_obstacles_and_turn():
    """
    Continuously checks the block the player is looking at for obstacles and handles them.
    Exits immediately if the script is stopped.
    """
    if not config["script_running"]:
        return

    targeted_block = player_get_targeted_block()

    if targeted_block:
        block_type = targeted_block.type.split("[")[0]
        block_side = targeted_block.side

        # Call handle_mine_reset only for bedrock with side='up'
        if block_type == "minecraft:bedrock" and block_side == "up":
            print("Bedrock with side='up' detected during obstacle check.")
            handle_mine_reset()
            return

        # Handle other obstacles normally
        if block_type in OBSTACLES:
            handle_obstacle()


def handle_mine_reset():
    """
    Handles mine reset logic for bedrock with side='up'.
    Only triggers when necessary, and avoids running on script start.
    """
    if not config["script_running"]:
        print("Script not running. Skipping mine reset.")
        return False  # Exit if the script is stopped

    targeted_block = player_get_targeted_block()
    if targeted_block:
        block_type = targeted_block.type.split("[")[0]
        block_side = targeted_block.side

        # Trigger reset only for bedrock with side='up'
        if block_type == "minecraft:bedrock" and block_side == "up":
            print("Bedrock with side='up' detected. Triggering mine reset.")
            stop_all_movements()
            execute("/mine y")
            time.sleep(5)
            follow_reset_sequence()
            return True

    print("No bedrock with side='up' detected. Continuing mining.")
    return False


def is_block_in_front():
    x, y, z = player_position()
    yaw, current_pitch = player_orientation()

    yaw_radians = math.radians(yaw)
    direction_x = round(-math.sin(yaw_radians))
    direction_z = round(math.cos(yaw_radians))

    def blocks_in_grid():
        for depth in range(1, 4):
            for offset in [-1, 0, 1]:
                for height in [0, 1]:
                    block_x = x + (direction_x * depth) + (direction_z * offset)
                    block_z = z + (direction_z * depth) + (direction_x * offset)
                    block_y = y + height
                    block = getblock(block_x, block_y, block_z)
                    if block and block.strip() != "minecraft:air":
                        return True
        return False

    if blocks_in_grid():
        return True

    current_pitch = player_orientation()[1]
    target_pitch = 30
    steps = 15
    delay = 0.01

    for step in range(steps + 1):
        interpolated_pitch = current_pitch + (step / steps) * (target_pitch - current_pitch)
        player_set_orientation(yaw, interpolated_pitch)
        time.sleep(delay)

    return False

def stop_all_movements():
    """
    Stops all ongoing player actions like forward movement, attacking, etc.
    Safely handles stopping to prevent crashes.
    """
    print("Stopping all movements...")
    player_press_forward(False)
    player_press_attack(False)
    player_press_left(False)
    player_press_right(False)
    player_press_jump(False)
    print("All movements stopped.")

def safe_sleep(seconds):
    """
    Sleeps in small intervals to allow checking script_running flag.
    """
    interval = 0.1  # Sleep in small intervals
    for _ in range(int(seconds / interval)):
        if not config["script_running"]:
            print("Exiting sleep early due to script stop.")
            return
        time.sleep(interval)


def follow_reset_sequence():
    """
    Perform the reset sequence: move forward, detect shroomlight/bedrock, and jump.
    Only runs if the script is active.
    """
    if not config["script_running"]:
        print("Script is not running. Skipping reset sequence.")
        return

    print("Starting reset sequence...")
    config["restart_in_progress"] = True  # Signal reset sequence in progress
    player_press_forward(True)

    while True:
        x, y, z = player_position()
        block_below = getblock(x, y - 1, z)

        if block_below and block_below.strip() in ["minecraft:shroomlight", "minecraft:bedrock"]:
            print(f"Block below detected: {block_below.strip()}. Jumping.")
            player_press_jump(True)
            time.sleep(0.5)
            player_press_jump(False)
            break

    player_press_forward(True)
    time.sleep(3)  # Continue moving forward for 3 seconds
    player_press_forward(False)

    config["restart_in_progress"] = False  # Reset sequence complete
    print("Reset sequence completed. Resuming mining.")
    hold_forward_and_mine()  # Explicitly resume mining


def smooth_look_away():
    #player_press_attack(True)
    current_yaw, _ = player_orientation()
    target_yaw = current_yaw + 180

    steps = 18 
    delay = 0.01
    for step in range(steps + 1):
        interpolated_yaw = current_yaw + (step / steps) * (target_yaw - current_yaw)
        player_set_orientation(interpolated_yaw, 26)
        time.sleep(delay)


def hold_forward_and_mine():
    """
    Mines while holding forward, ensuring continuous motion during cursor movements.
    Stops immediately when the script_running flag is set to False.
    """
    while config["script_running"]:
        if is_block_in_front():
            player_press_forward(True)
            player_press_attack(True)

            # Perform mining and obstacle checks
            start_time = time.time()
            while time.time() - start_time < 1:
                if not config["script_running"]:
                    stop_all_movements()
                    return
                check_for_obstacles_and_turn()
                smooth_cursor_movement()
        else:
            # No blocks detected, move forward while breaking
            player_press_forward(True)
            player_press_attack(True)

        if not config["script_running"]:
            stop_all_movements()
            return
        time.sleep(0.1)

    # Ensure movements are stopped if the loop exits
    stop_all_movements()


def stop_forward_movement():
    player_press_forward(False)


def check_inventory_and_sell():
    inventory = player_inventory()
    for item in inventory:
        if item.slot == 35:
            execute_sellall_nonblocking()
            break


def execute_sellall_nonblocking():
    execute("/sellall")
    for _ in range(20):
        time.sleep(0.1)
        check_for_obstacles_and_turn()


def smooth_cursor_movement():
    """
    Perform smooth cursor movement while respecting the script's running state.
    Ensures no delay before and after the snap movement with pitch adjustments.
    """
    current_yaw, current_pitch = player_orientation()

    # Define the movement sequence
    sequence = [
        {"yaw_offset": 27.5, "pitch_offset": 6, "steps": max(1, config["default_steps"] - 2), "delay": 0.006},
        {"yaw_offset": -27.5, "pitch_offset": 6, "steps": max(1, config["default_steps"] - 2), "delay": 0.006},
        {"yaw_offset": -60, "pitch_offset": 0, "steps": max(1, config["default_steps"] + 5), "delay": 0.008},
        {"yaw_offset": -27.5, "pitch_offset": -6, "steps": max(1, config["default_steps"] - 2), "delay": 0.006},
        {"yaw_offset": 27.5, "pitch_offset": -6, "steps": max(1, config["default_steps"] - 2), "delay": 0.006},
        {"yaw_offset": 60, "pitch_offset": 14 - current_pitch, "steps": max(1, config["default_steps"] + 5), "delay": 0.00001},  # Snap movement
    ]

    # Perform the sequence
    for i, movement in enumerate(sequence):
        if not config["script_running"]:  # Stop immediately if the script is not running
            return

        target_yaw = current_yaw + movement["yaw_offset"]
        target_pitch = current_pitch + movement["pitch_offset"]
        steps = max(1, movement["steps"])
        delay = movement["delay"]

        # Smoothly transition to the target yaw and pitch
        for step in range(steps + 1):
            if not config["script_running"]:
                return

            interpolated_yaw = current_yaw + (step / steps) * (target_yaw - current_yaw)
            interpolated_pitch = current_pitch + (step / steps) * (target_pitch - current_pitch)
            player_set_orientation(interpolated_yaw, interpolated_pitch)

            # Skip delay for snap movement
            if movement["pitch_offset"] == 14 - current_pitch:
                continue

            # Apply delay for other movements
            if step < steps and delay > 0:
                time.sleep(delay)

        # Update the current yaw and pitch for the next movement
        current_yaw = target_yaw
        current_pitch = target_pitch

        # Check for obstacles after completing each movement except the snap movement
        if movement["pitch_offset"] != 14 - current_pitch:
            check_for_obstacles_and_turn()




def obstacle_detected():
    targeted_block = player_get_targeted_block()
    if targeted_block:
        block_type = targeted_block.type.split("[")[0]
        if block_type in OBSTACLES:
            return True
    return False


def handle_obstacle():
    """
    Handles detected obstacles by stopping movements and turning away.
    Avoids 180-degree turns during or immediately after reset sequences.
    """
    if config.get("restart_in_progress", False):
        return  # Skip obstacle handling if a reset is in progress

    print("Obstacle detected. Stopping and turning.")
    stop_all_movements()
    current_yaw, current_pitch = player_orientation()
    turn_180(current_yaw, current_pitch)  # Perform a 180-degree turn
    hold_forward_and_mine()  # Resume mining



def turn_180(current_yaw, current_pitch):
    target_yaw = current_yaw + 180

    steps = 18
    delay = 0.01
    for step in range(steps + 1):
        interpolated_yaw = current_yaw + (step / steps) * (target_yaw - current_yaw)
        player_set_orientation(interpolated_yaw, current_pitch)
        time.sleep(delay)


def auto_mining_loop():
    """
    Main loop for auto-mining. Handles clean exit when the script is stopped.
    """
    try:
        print("Starting auto-mining loop.")
        while config["script_running"]:
            print("Mining loop iteration...")
            player_press_forward(True)
            player_press_attack(True)

            # Handle resets and obstacles
            if handle_mine_reset():
                print("Mine reset triggered.")
                continue

            check_for_obstacles_and_turn()
            check_inventory_and_sell()
            smooth_cursor_movement()

            if not config["script_running"]:  # Double-check before sleep
                print("Script running flag set to False. Exiting loop.")
                break

            time.sleep(0.05)  # Short delay for rapid checks
    except Exception as e:
        print(f"Error in auto_mining_loop: {e}")
    finally:
        print("Exiting auto-mining loop.")
        stop_all_movements()


if __name__ == "__main__":
    try:
        # Set CustomTkinter appearance
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")

        # Initialize the modern GUI
        app = ModernMiningGUI()

        # Start the GUI main loop
        app.mainloop()

    except Exception as e:
        echo(f"§c[Error] Failed to start application: {e}")
        import traceback
        traceback.print_exc()





