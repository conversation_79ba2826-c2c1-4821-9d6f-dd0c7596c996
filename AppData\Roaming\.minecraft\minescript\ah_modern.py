#!/usr/bin/env python3
"""
Advanced Mining Automation Script v2.0
Modern, feature-rich mining bot with comprehensive GUI and safety features.

Features:
- Modern scrollable GUI with organized sections
- Profile-based configuration system
- Advanced movement patterns with human-like behavior
- Comprehensive statistics tracking
- Enhanced safety features and boundary detection
- Robust auto-selling system with configurable triggers
- Session persistence and recovery
- Real-time status monitoring
"""

import time
import customtkinter as ctk
import threading
import random
import math
import os
import sys
import json
import traceback
import queue
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum

# Minescript imports
from minescript import (
    player_press_forward,
    player_press_attack,
    player_press_left,
    player_press_right,
    player_press_jump,
    player_set_orientation,
    player_position,
    player_get_targeted_block,
    player_orientation,
    player_inventory,
    getblock,
    chat,
    echo,
    execute
)

# Constants
VERSION = "2.0"
CONFIG_FILE = "mining_config.json"
STATS_FILE = "mining_stats.json"
PROFILES_FILE = "mining_profiles.json"
SESSION_FILE = "mining_session.json"

# Enums for better type safety
class MiningState(Enum):
    STOPPED = "stopped"
    STARTING = "starting"
    MINING = "mining"
    RESETTING = "resetting"
    SELLING = "selling"
    PAUSED = "paused"
    ERROR = "error"

class MovementPattern(Enum):
    CONSERVATIVE = "conservative"
    AGGRESSIVE = "aggressive"
    RANDOM = "random"
    HUMAN_LIKE = "human_like"
    PRISON_MINING = "prison_mining"

class SellTrigger(Enum):
    SLOT_BASED = "slot_based"
    PERCENTAGE = "percentage"
    ITEM_TYPE = "item_type"
    TIME_BASED = "time_based"

# Global state management
class MiningStateManager:
    """Thread-safe state management for the mining bot."""
    
    def __init__(self):
        self._lock = threading.RLock()
        self._state = MiningState.STOPPED
        self._current_action = "Idle"
        self._error_message = ""
        self._stats_queue = queue.Queue()
        
    def get_state(self) -> MiningState:
        with self._lock:
            return self._state
    
    def set_state(self, state: MiningState, action: str = ""):
        with self._lock:
            self._state = state
            if action:
                self._current_action = action
    
    def get_current_action(self) -> str:
        with self._lock:
            return self._current_action
    
    def set_error(self, message: str):
        with self._lock:
            self._error_message = message
            self._state = MiningState.ERROR
    
    def get_error(self) -> str:
        with self._lock:
            return self._error_message
    
    def is_running(self) -> bool:
        with self._lock:
            return self._state in [MiningState.MINING, MiningState.RESETTING, MiningState.SELLING]

# Data classes for configuration
@dataclass
class MovementConfig:
    """Configuration for movement patterns and behavior."""
    pattern: MovementPattern = MovementPattern.HUMAN_LIKE
    speed: float = 0.005
    steps: int = 10
    randomization: float = 0.2
    micro_movements: bool = True
    anti_detection_delay: Tuple[float, float] = (0.05, 0.15)

    # Enhanced human-like movement parameters
    smooth_transitions: bool = True
    transition_steps: int = 3  # Number of steps for smooth transitions
    hesitation_chance: float = 0.15  # 15% chance of brief hesitation
    hesitation_duration: Tuple[float, float] = (0.1, 0.4)  # Random hesitation time
    variable_timing: bool = True
    timing_variance: Tuple[float, float] = (0.1, 0.3)  # Random delay range
    path_deviation: bool = True
    deviation_strength: float = 0.3  # How much to deviate from perfect paths
    reaction_delay: Tuple[float, float] = (0.05, 0.2)  # Human reaction time
    micro_corrections: bool = True
    correction_chance: float = 0.25  # 25% chance of small corrections

    # Prison mining specific parameters
    continuous_forward: bool = True  # Never stop forward movement
    scanning_range: float = 45.0  # Left-right scanning angle range
    scanning_speed: float = 0.8  # Speed of left-right head movement
    scanning_smoothness: int = 20  # Steps for smooth scanning transitions
    tunnel_focus: bool = True  # Focus on tunnel-like movement patterns
    seamless_transitions: bool = True  # No pauses between direction changes
    cursor_smoothness: float = 0.8

@dataclass
class SafetyConfig:
    """Safety and boundary configuration."""
    enable_boundaries: bool = True
    boundary_x: Tuple[int, int] = (-100, 100)
    boundary_y: Tuple[int, int] = (0, 255)
    boundary_z: Tuple[int, int] = (-100, 100)
    emergency_stop_key: str = "F1"
    stuck_detection_time: float = 3.0
    enable_audio_alarms: bool = True

@dataclass
class SellingConfig:
    """Auto-selling system configuration."""
    enabled: bool = True
    trigger_type: SellTrigger = SellTrigger.SLOT_BASED
    trigger_slots: List[int] = None
    trigger_percentage: float = 90.0
    trigger_items: List[str] = None
    cooldown_seconds: float = 5.0
    retry_attempts: int = 3
    commands: List[str] = None
    
    def __post_init__(self):
        if self.trigger_slots is None:
            self.trigger_slots = [35]
        if self.trigger_items is None:
            self.trigger_items = []
        if self.commands is None:
            self.commands = ["/sellall"]

@dataclass
class ResetConfig:
    """Mine reset handling configuration."""
    enabled: bool = True
    detection_blocks: List[str] = None
    reset_commands: List[str] = None
    reset_delay: float = 5.0
    retry_attempts: int = 3
    failure_action: str = "stop"  # stop, retry, ignore
    
    def __post_init__(self):
        if self.detection_blocks is None:
            self.detection_blocks = ["minecraft:bedrock"]
        if self.reset_commands is None:
            self.reset_commands = ["/mine y"]

@dataclass
class MiningProfile:
    """Complete mining profile configuration."""
    name: str = "Default"
    description: str = "Default mining profile"
    movement: MovementConfig = None
    safety: SafetyConfig = None
    selling: SellingConfig = None
    reset: ResetConfig = None
    obstacles: List[str] = None
    
    def __post_init__(self):
        if self.movement is None:
            self.movement = MovementConfig()
        if self.safety is None:
            self.safety = SafetyConfig()
        if self.selling is None:
            self.selling = SellingConfig()
        if self.reset is None:
            self.reset = ResetConfig()
        if self.obstacles is None:
            self.obstacles = ["minecraft:bedrock", "minecraft:ladder", "minecraft:shroomlight"]

@dataclass
class SessionStats:
    """Statistics for the current mining session."""
    start_time: datetime = None
    blocks_mined: int = 0
    resets_performed: int = 0
    items_sold: int = 0
    sell_attempts: int = 0
    sell_failures: int = 0
    runtime_seconds: float = 0.0
    distance_traveled: float = 0.0
    obstacles_encountered: int = 0
    
    def __post_init__(self):
        if self.start_time is None:
            self.start_time = datetime.now()

@dataclass
class OverallStats:
    """Overall statistics across all sessions."""
    total_runtime: float = 0.0
    total_blocks_mined: int = 0
    total_resets: int = 0
    total_items_sold: int = 0
    sessions_completed: int = 0
    last_session: datetime = None
    
    def __post_init__(self):
        if self.last_session is None:
            self.last_session = datetime.now()

# Configuration management class
class ConfigManager:
    """Manages configuration loading, saving, and validation."""

    def __init__(self):
        self.current_profile = "default"
        self.profiles: Dict[str, MiningProfile] = {}
        self.session_stats = SessionStats()
        self.overall_stats = OverallStats()
        self._load_all_configs()

    def _load_all_configs(self):
        """Load all configuration files."""
        self._load_profiles()
        self._load_stats()
        self._migrate_old_config()

    def _load_profiles(self):
        """Load mining profiles from file."""
        try:
            if os.path.exists(PROFILES_FILE):
                with open(PROFILES_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                    # Check if the file contains corrupted enum data
                    if self._has_corrupted_enum_data(data):
                        echo("§e[Config] Detected corrupted enum data in profiles, regenerating...")
                        self._backup_and_regenerate_profiles()
                        return

                    for name, profile_data in data.items():
                        self.profiles[name] = self._dict_to_profile(profile_data)

                    echo("§a[Config] Profiles loaded successfully")
            else:
                # Create default profiles
                echo("§e[Config] No profiles file found, creating defaults")
                self._create_default_profiles()
                self.save_profiles()
        except Exception as e:
            echo(f"§c[Config] Error loading profiles: {e}")
            self._backup_and_regenerate_profiles()

    def _has_corrupted_enum_data(self, data):
        """Check if the configuration data contains corrupted enum representations."""
        try:
            for profile_name, profile_data in data.items():
                if isinstance(profile_data, dict):
                    # Check movement pattern
                    if 'movement' in profile_data and isinstance(profile_data['movement'], dict):
                        pattern = profile_data['movement'].get('pattern', '')
                        if isinstance(pattern, str) and ('MovementPattern.' in pattern or 'SellTrigger.' in pattern):
                            return True

                    # Check selling trigger
                    if 'selling' in profile_data and isinstance(profile_data['selling'], dict):
                        trigger = profile_data['selling'].get('trigger_type', '')
                        if isinstance(trigger, str) and ('MovementPattern.' in trigger or 'SellTrigger.' in trigger):
                            return True
            return False
        except:
            return True  # If we can't parse it, consider it corrupted

    def _backup_and_regenerate_profiles(self):
        """Backup corrupted profiles and create fresh ones."""
        try:
            if os.path.exists(PROFILES_FILE):
                import time
                backup_file = f"{PROFILES_FILE}.backup_{int(time.time())}"
                os.rename(PROFILES_FILE, backup_file)
                echo(f"§e[Config] Backed up corrupted profiles to {backup_file}")

            self._create_default_profiles()
            self.save_profiles()
            echo("§a[Config] Fresh profiles created successfully")
        except Exception as e:
            echo(f"§c[Config] Error during profile regeneration: {e}")
            self._create_default_profiles()

    def _create_default_profiles(self):
        """Create default mining profiles."""
        # Conservative profile
        conservative = MiningProfile(
            name="Conservative",
            description="Safe mining with slower movements and extra safety checks",
            movement=MovementConfig(
                pattern=MovementPattern.CONSERVATIVE,
                speed=0.008,
                steps=15,
                randomization=0.1,
                smooth_transitions=True,
                transition_steps=4,
                hesitation_chance=0.20,
                hesitation_duration=(0.2, 0.5),
                variable_timing=True,
                timing_variance=(0.15, 0.35),
                path_deviation=True,
                deviation_strength=0.2,
                reaction_delay=(0.1, 0.25),
                micro_corrections=True,
                correction_chance=0.30
            )
        )

        # Aggressive profile
        aggressive = MiningProfile(
            name="Aggressive",
            description="Fast mining with quick movements for maximum efficiency",
            movement=MovementConfig(
                pattern=MovementPattern.AGGRESSIVE,
                speed=0.003,
                steps=8,
                randomization=0.3,
                smooth_transitions=True,
                transition_steps=2,
                hesitation_chance=0.08,
                hesitation_duration=(0.05, 0.15),
                variable_timing=True,
                timing_variance=(0.05, 0.2),
                path_deviation=True,
                deviation_strength=0.4,
                reaction_delay=(0.02, 0.1),
                micro_corrections=True,
                correction_chance=0.15
            )
        )

        # Default profile (Human-like)
        default = MiningProfile(
            name="Default",
            description="Balanced mining profile with advanced human-like behavior",
            movement=MovementConfig(
                pattern=MovementPattern.HUMAN_LIKE,
                speed=0.005,
                steps=10,
                randomization=0.25,
                smooth_transitions=True,
                transition_steps=3,
                hesitation_chance=0.15,
                hesitation_duration=(0.1, 0.4),
                variable_timing=True,
                timing_variance=(0.1, 0.3),
                path_deviation=True,
                deviation_strength=0.3,
                reaction_delay=(0.05, 0.2),
                micro_corrections=True,
                correction_chance=0.25
            )
        )

        # Prison Mining profile
        prison_mining = MiningProfile(
            name="Prison Mining",
            description="Authentic prison server mining with continuous forward movement and left-right scanning",
            movement=MovementConfig(
                pattern=MovementPattern.PRISON_MINING,
                speed=0.002,  # Faster for continuous movement
                steps=1,  # Not used in prison mining
                randomization=0.1,
                smooth_transitions=True,
                continuous_forward=True,
                scanning_range=45.0,
                scanning_speed=0.8,
                scanning_smoothness=20,
                tunnel_focus=True,
                seamless_transitions=True,
                # Disable features that interrupt continuous movement
                hesitation_chance=0.0,
                variable_timing=False,
                path_deviation=False,
                micro_corrections=False
            )
        )

        self.profiles = {
            "default": default,
            "conservative": conservative,
            "aggressive": aggressive,
            "prison_mining": prison_mining
        }

    def _dict_to_profile(self, data: Dict) -> MiningProfile:
        """Convert dictionary data to MiningProfile object."""
        try:
            # Handle nested dataclass conversion with robust enum handling
            if 'movement' in data and isinstance(data['movement'], dict):
                movement_data = data['movement'].copy()
                # Convert string pattern back to enum with robust handling
                if 'pattern' in movement_data and isinstance(movement_data['pattern'], str):
                    movement_data['pattern'] = self._safe_enum_conversion(
                        movement_data['pattern'], MovementPattern, MovementPattern.HUMAN_LIKE
                    )
                data['movement'] = MovementConfig(**movement_data)

            if 'safety' in data and isinstance(data['safety'], dict):
                data['safety'] = SafetyConfig(**data['safety'])

            if 'selling' in data and isinstance(data['selling'], dict):
                selling_data = data['selling'].copy()
                # Convert string trigger_type back to enum with robust handling
                if 'trigger_type' in selling_data and isinstance(selling_data['trigger_type'], str):
                    selling_data['trigger_type'] = self._safe_enum_conversion(
                        selling_data['trigger_type'], SellTrigger, SellTrigger.PERCENTAGE
                    )
                data['selling'] = SellingConfig(**selling_data)

            if 'reset' in data and isinstance(data['reset'], dict):
                data['reset'] = ResetConfig(**data['reset'])

            return MiningProfile(**data)
        except Exception as e:
            echo(f"§c[Config] Error converting profile data: {e}")
            return MiningProfile()

    def _safe_enum_conversion(self, value_str: str, enum_class, default_value):
        """Safely convert string to enum, handling both enum names and values."""
        try:
            # First, try direct enum value conversion (e.g., "human_like" -> MovementPattern.HUMAN_LIKE)
            return enum_class(value_str)
        except ValueError:
            try:
                # If that fails, try to extract enum name from full representation
                # Handle cases like "MovementPattern.HUMAN_LIKE" -> "HUMAN_LIKE"
                if '.' in value_str:
                    enum_name = value_str.split('.')[-1]  # Get the part after the last dot
                    # Try to get enum by name
                    return enum_class[enum_name]
                else:
                    # Try to get enum by name directly
                    return enum_class[value_str.upper()]
            except (ValueError, KeyError):
                try:
                    # Last resort: try to match by enum name case-insensitively
                    for enum_item in enum_class:
                        if enum_item.name.upper() == value_str.upper():
                            return enum_item
                        if enum_item.value.upper() == value_str.upper():
                            return enum_item
                except:
                    pass

                # If all else fails, use default and log warning
                echo(f"§e[Config] Warning: Invalid enum value '{value_str}' for {enum_class.__name__}, using default")
                return default_value

    def _load_stats(self):
        """Load statistics from file."""
        try:
            if os.path.exists(STATS_FILE):
                with open(STATS_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if 'overall' in data:
                        # Convert datetime strings back to datetime objects
                        overall_data = data['overall']
                        if 'last_session' in overall_data:
                            overall_data['last_session'] = datetime.fromisoformat(overall_data['last_session'])
                        self.overall_stats = OverallStats(**overall_data)
        except Exception as e:
            echo(f"§c[Config] Error loading stats: {e}")
            self.overall_stats = OverallStats()

    def _migrate_old_config(self):
        """Migrate old prisonconfig.txt to new system."""
        old_config_file = "prisonconfig.txt"
        if os.path.exists(old_config_file):
            try:
                echo("§e[Config] Migrating old configuration...")
                with open(old_config_file, 'r') as f:
                    lines = f.readlines()
                    for line in lines:
                        if '=' in line:
                            key, value = line.strip().split('=', 1)
                            if key == "default_steps":
                                # Migrate to movement config
                                if "default" in self.profiles:
                                    self.profiles["default"].movement.steps = int(value)

                # Save migrated config and remove old file
                self.save_profiles()
                os.rename(old_config_file, f"{old_config_file}.backup")
                echo("§a[Config] Migration completed successfully")
            except Exception as e:
                echo(f"§c[Config] Error during migration: {e}")

    def save_profiles(self):
        """Save all profiles to file."""
        try:
            data = {}
            for name, profile in self.profiles.items():
                profile_dict = asdict(profile)
                # Convert enums to their string values for JSON serialization
                self._convert_enums_to_values(profile_dict)
                data[name] = profile_dict

            with open(PROFILES_FILE, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            echo(f"§c[Config] Error saving profiles: {e}")

    def _convert_enums_to_values(self, data):
        """Recursively convert enum objects to their string values."""
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, Enum):
                    data[key] = value.value
                elif isinstance(value, dict):
                    self._convert_enums_to_values(value)
                elif isinstance(value, list):
                    for i, item in enumerate(value):
                        if isinstance(item, Enum):
                            value[i] = item.value
                        elif isinstance(item, dict):
                            self._convert_enums_to_values(item)

    def save_stats(self):
        """Save statistics to file."""
        try:
            data = {
                'overall': asdict(self.overall_stats),
                'session': asdict(self.session_stats)
            }

            # Convert enums to their string values
            self._convert_enums_to_values(data)

            with open(STATS_FILE, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, default=str)
        except Exception as e:
            echo(f"§c[Config] Error saving stats: {e}")

    def get_current_profile(self) -> MiningProfile:
        """Get the currently active profile."""
        return self.profiles.get(self.current_profile, self.profiles["default"])

    def reset_configuration(self):
        """Reset all configuration files to defaults (useful for fixing corruption)."""
        try:
            import time
            timestamp = int(time.time())

            # Backup existing files
            for file_path in [PROFILES_FILE, STATS_FILE, CONFIG_FILE, SESSION_FILE]:
                if os.path.exists(file_path):
                    backup_path = f"{file_path}.backup_{timestamp}"
                    os.rename(file_path, backup_path)
                    echo(f"§e[Config] Backed up {file_path} to {backup_path}")

            # Recreate defaults
            self._create_default_profiles()
            self.session_stats = SessionStats()
            self.overall_stats = OverallStats()

            # Save fresh configuration
            self.save_profiles()
            self.save_stats()

            echo("§a[Config] Configuration reset to defaults successfully")
        except Exception as e:
            echo(f"§c[Config] Error resetting configuration: {e}")

    def set_current_profile(self, profile_name: str):
        """Set the current active profile."""
        if profile_name in self.profiles:
            self.current_profile = profile_name
            echo(f"§a[Config] Switched to profile: {profile_name}")
        else:
            echo(f"§c[Config] Profile not found: {profile_name}")

# Modern Movement System
class ModernMovementController:
    """Advanced movement controller with human-like patterns."""

    def __init__(self, profile: MiningProfile):
        self.profile = profile
        self.last_position = None
        self.stuck_start_time = None
        self.movement_history = []

    def update_profile(self, profile: MiningProfile):
        """Update the movement profile."""
        self.profile = profile

    def get_smooth_cursor_sequence(self) -> List[Dict]:
        """Generate movement sequence based on current profile."""
        config = self.profile.movement
        base_steps = config.steps

        # Apply randomization
        if config.randomization > 0:
            variation = random.uniform(-config.randomization, config.randomization)
            steps = max(5, int(base_steps * (1 + variation)))
        else:
            steps = base_steps

        # Pattern-specific sequences
        if config.pattern == MovementPattern.CONSERVATIVE:
            return self._get_conservative_sequence(steps)
        elif config.pattern == MovementPattern.AGGRESSIVE:
            return self._get_aggressive_sequence(steps)
        elif config.pattern == MovementPattern.RANDOM:
            return self._get_random_sequence(steps)
        elif config.pattern == MovementPattern.PRISON_MINING:
            return self._get_prison_mining_sequence()
        else:  # HUMAN_LIKE
            return self._get_human_like_sequence(steps)

    def _get_conservative_sequence(self, steps: int) -> List[Dict]:
        """Conservative movement pattern with enhanced human-like safety focus."""
        config = self.profile.movement
        base_movements = [
            {"yaw_offset": 19.8, "pitch_offset": 4.2, "steps": steps + 3},
            {"yaw_offset": -20.3, "pitch_offset": 3.9, "steps": steps + 3},
            {"yaw_offset": -39.7, "pitch_offset": 0.1, "steps": steps + 5},
            {"yaw_offset": -19.9, "pitch_offset": -4.1, "steps": steps + 3},
            {"yaw_offset": 20.1, "pitch_offset": -3.8, "steps": steps + 3},
            {"yaw_offset": 40.2, "pitch_offset": 9.9, "steps": steps + 5},
        ]

        return self._apply_human_characteristics(base_movements, config, conservative=True)

    def _get_aggressive_sequence(self, steps: int) -> List[Dict]:
        """Aggressive movement pattern with human-like efficiency focus."""
        config = self.profile.movement
        base_movements = [
            {"yaw_offset": 34.7, "pitch_offset": 8.1, "steps": max(1, steps - 3)},
            {"yaw_offset": -35.2, "pitch_offset": 7.9, "steps": max(1, steps - 3)},
            {"yaw_offset": -69.8, "pitch_offset": -0.2, "steps": max(1, steps + 2)},
            {"yaw_offset": -34.9, "pitch_offset": -8.3, "steps": max(1, steps - 3)},
            {"yaw_offset": 35.1, "pitch_offset": -7.8, "steps": max(1, steps - 3)},
            {"yaw_offset": 70.3, "pitch_offset": 15.7, "steps": max(1, steps + 2)},
        ]

        return self._apply_human_characteristics(base_movements, config, aggressive=True)

    def _get_random_sequence(self, steps: int) -> List[Dict]:
        """Random movement pattern with human-like unpredictability."""
        config = self.profile.movement
        base_movements = []

        for _ in range(6):
            base_movements.append({
                "yaw_offset": random.uniform(-45, 45),
                "pitch_offset": random.uniform(-10, 10),
                "steps": random.randint(max(1, steps - 5), steps + 5),
            })

        return self._apply_human_characteristics(base_movements, config, random_pattern=True)

    def _get_prison_mining_sequence(self) -> List[Dict]:
        """Prison mining pattern with continuous forward movement and left-right scanning."""
        config = self.profile.movement

        # Prison mining uses continuous scanning rather than discrete movements
        # This returns a single continuous movement instruction
        return [{
            "type": "prison_mining",
            "continuous_forward": True,
            "scanning_range": config.scanning_range,
            "scanning_speed": config.scanning_speed,
            "scanning_smoothness": config.scanning_smoothness,
            "tunnel_focus": config.tunnel_focus,
            "seamless_transitions": config.seamless_transitions,
            "base_delay": config.speed
        }]

    def _apply_human_characteristics(self, base_movements: List[Dict], config: MovementConfig,
                                   conservative: bool = False, aggressive: bool = False,
                                   random_pattern: bool = False) -> List[Dict]:
        """Apply human-like characteristics to any movement pattern."""
        sequence = []

        for i, movement in enumerate(base_movements):
            enhanced_movement = movement.copy()

            # Apply pattern-specific timing
            if conservative:
                base_delay = random.uniform(0.15, 0.35)
                hesitation_multiplier = 1.5
            elif aggressive:
                base_delay = random.uniform(0.05, 0.2)
                hesitation_multiplier = 0.5
            elif random_pattern:
                base_delay = random.uniform(0.08, 0.25)
                hesitation_multiplier = 1.0
            else:
                base_delay = random.uniform(0.1, 0.3)
                hesitation_multiplier = 1.0

            # Add human-like imperfections
            if config.path_deviation:
                deviation = config.deviation_strength
                enhanced_movement["yaw_offset"] += random.uniform(-deviation * 3, deviation * 3)
                enhanced_movement["pitch_offset"] += random.uniform(-deviation * 1.5, deviation * 1.5)

            # Add micro-movements
            if config.micro_movements:
                enhanced_movement["yaw_offset"] += random.uniform(-1.2, 1.2)
                enhanced_movement["pitch_offset"] += random.uniform(-0.6, 0.6)

            # Set timing with human-like variation
            enhanced_movement["delay"] = base_delay
            enhanced_movement["reaction_delay"] = random.uniform(*config.reaction_delay)
            enhanced_movement["smooth_transition"] = config.smooth_transitions
            enhanced_movement["transition_steps"] = config.transition_steps
            enhanced_movement["needs_correction"] = (
                config.micro_corrections and random.random() < config.correction_chance
            )

            sequence.append(enhanced_movement)

            # Add hesitation between movements
            if config.hesitation_chance > 0 and random.random() < (config.hesitation_chance * hesitation_multiplier):
                hesitation_time = random.uniform(*config.hesitation_duration)
                sequence.append({
                    "type": "hesitation",
                    "duration": hesitation_time,
                    "yaw_offset": 0,
                    "pitch_offset": 0,
                    "steps": 0,
                    "delay": hesitation_time
                })

        return sequence

    def _get_human_like_sequence(self, steps: int) -> List[Dict]:
        """Advanced human-like movement with natural variations and imperfections."""
        config = self.profile.movement

        # Base movement pattern with intentional imperfections
        base_movements = [
            {"yaw_offset": 28.3, "pitch_offset": 5.7, "steps": steps - 1},
            {"yaw_offset": -26.8, "pitch_offset": 6.2, "steps": steps - 2},
            {"yaw_offset": -58.5, "pitch_offset": -0.3, "steps": steps + 4},
            {"yaw_offset": -29.1, "pitch_offset": -5.9, "steps": steps - 1},
            {"yaw_offset": 26.4, "pitch_offset": -6.4, "steps": steps - 2},
            {"yaw_offset": 61.2, "pitch_offset": 13.8, "steps": steps + 3},
        ]

        sequence = []
        for i, movement in enumerate(base_movements):
            # Create enhanced movement with human-like characteristics
            enhanced_movement = self._create_human_like_movement(movement, config, i)
            sequence.append(enhanced_movement)

            # Add occasional hesitation between movements
            if config.hesitation_chance > 0 and random.random() < config.hesitation_chance:
                hesitation_time = random.uniform(*config.hesitation_duration)
                sequence.append({
                    "type": "hesitation",
                    "duration": hesitation_time,
                    "yaw_offset": 0,
                    "pitch_offset": 0,
                    "steps": 0,
                    "delay": hesitation_time
                })

        return sequence

    def _create_human_like_movement(self, base_movement: Dict, config: MovementConfig, index: int) -> Dict:
        """Create a single human-like movement with all natural characteristics."""
        movement = base_movement.copy()

        # Add path deviation (humans don't move in perfect lines)
        if config.path_deviation:
            deviation = config.deviation_strength
            movement["yaw_offset"] += random.uniform(-deviation * 5, deviation * 5)
            movement["pitch_offset"] += random.uniform(-deviation * 2, deviation * 2)

        # Add micro-movements and corrections
        if config.micro_movements:
            movement["yaw_offset"] += random.uniform(-1.5, 1.5)
            movement["pitch_offset"] += random.uniform(-0.8, 0.8)

        # Variable timing based on human reaction patterns
        if config.variable_timing:
            base_delay = random.uniform(*config.timing_variance)
            # Add slight delay variation based on movement complexity
            complexity_factor = abs(movement["yaw_offset"]) / 60.0  # Normalize to 0-1
            movement["delay"] = base_delay * (1 + complexity_factor * 0.3)
        else:
            movement["delay"] = 0.006

        # Add reaction delay (humans don't react instantly)
        movement["reaction_delay"] = random.uniform(*config.reaction_delay)

        # Add micro-corrections chance
        movement["needs_correction"] = (
            config.micro_corrections and
            random.random() < config.correction_chance
        )

        # Smooth transitions flag
        movement["smooth_transition"] = config.smooth_transitions
        movement["transition_steps"] = config.transition_steps

        return movement

    def check_stuck_detection(self) -> bool:
        """Check if player is stuck and needs intervention."""
        current_pos = player_position()
        current_time = time.time()

        if self.last_position is None:
            self.last_position = current_pos
            return False

        # Calculate distance moved
        distance = math.sqrt(
            (current_pos[0] - self.last_position[0]) ** 2 +
            (current_pos[2] - self.last_position[2]) ** 2
        )

        # If moved very little, start/continue stuck timer
        if distance < 0.5:
            if self.stuck_start_time is None:
                self.stuck_start_time = current_time
            elif current_time - self.stuck_start_time > self.profile.safety.stuck_detection_time:
                return True
        else:
            # Reset stuck timer if moving
            self.stuck_start_time = None
            self.last_position = current_pos

        return False

# Auto-Selling System
class ModernSellingSystem:
    """Enhanced auto-selling system with configurable triggers."""

    def __init__(self, profile: MiningProfile):
        self.profile = profile
        self.last_sell_time = 0
        self.sell_attempts = 0
        self.sell_failures = 0

    def update_profile(self, profile: MiningProfile):
        """Update the selling profile."""
        self.profile = profile

    def should_sell(self) -> bool:
        """Check if selling should be triggered."""
        if not self.profile.selling.enabled:
            return False

        # Check cooldown
        current_time = time.time()
        if current_time - self.last_sell_time < self.profile.selling.cooldown_seconds:
            return False

        config = self.profile.selling

        if config.trigger_type == SellTrigger.SLOT_BASED:
            return self._check_slot_trigger()
        elif config.trigger_type == SellTrigger.PERCENTAGE:
            return self._check_percentage_trigger()
        elif config.trigger_type == SellTrigger.ITEM_TYPE:
            return self._check_item_type_trigger()
        elif config.trigger_type == SellTrigger.TIME_BASED:
            return self._check_time_trigger()

        return False

    def _check_slot_trigger(self) -> bool:
        """Check if specific slots are filled."""
        try:
            inventory = player_inventory()
            for item in inventory:
                if item.slot in self.profile.selling.trigger_slots:
                    return True
        except Exception as e:
            echo(f"§c[Selling] Error checking inventory: {e}")
        return False

    def _check_percentage_trigger(self) -> bool:
        """Check if inventory is filled to specified percentage."""
        try:
            inventory = player_inventory()
            filled_slots = len([item for item in inventory if item.slot >= 9])  # Exclude hotbar
            total_slots = 27  # Main inventory slots
            percentage = (filled_slots / total_slots) * 100
            return percentage >= self.profile.selling.trigger_percentage
        except Exception as e:
            echo(f"§c[Selling] Error checking inventory percentage: {e}")
        return False

    def execute_sell(self) -> bool:
        """Execute the selling commands."""
        try:
            self.sell_attempts += 1
            self.last_sell_time = time.time()

            for command in self.profile.selling.commands:
                execute(command)
                time.sleep(0.5)  # Small delay between commands

            echo("§a[Selling] Items sold successfully")
            CONFIG_MANAGER.session_stats.sell_attempts += 1
            return True

        except Exception as e:
            self.sell_failures += 1
            CONFIG_MANAGER.session_stats.sell_failures += 1
            echo(f"§c[Selling] Error executing sell: {e}")
            return False

# Modern GUI Implementation
class ModernMiningGUI(ctk.CTk):
    """Modern, comprehensive mining automation GUI with scrollable layout."""

    def __init__(self):
        super().__init__()

        # Initialize components
        self.config_manager = CONFIG_MANAGER
        self.state_manager = STATE_MANAGER
        self.current_profile = self.config_manager.get_current_profile()
        self.movement_controller = ModernMovementController(self.current_profile)
        self.selling_system = ModernSellingSystem(self.current_profile)

        # GUI state
        self.script_thread = None
        self.update_thread = None
        self.running = False

        # Configure window
        self.title(f"🔨 Advanced Mining Bot v{VERSION}")
        self.geometry("650x900")
        self.resizable(True, True)

        # Configure grid
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # Create main scrollable frame
        self.main_frame = ctk.CTkScrollableFrame(self)
        self.main_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        self.main_frame.grid_columnconfigure(0, weight=1)

        # Initialize GUI
        self.setup_ui()
        self.setup_status_updates()

        # Handle window close
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_ui(self):
        """Set up the complete user interface."""
        row = 0

        # Title section
        row = self.create_title_section(row)

        # Status section
        row = self.create_status_section(row)

        # Profile section
        row = self.create_profile_section(row)

        # Mining control section
        row = self.create_mining_control_section(row)

        # Movement settings section
        row = self.create_movement_section(row)

        # Safety features section
        row = self.create_safety_section(row)

        # Auto-selling section
        row = self.create_selling_section(row)

        # Statistics section
        row = self.create_statistics_section(row)

        # Chat/Command section
        row = self.create_chat_section(row)

        # Debug section
        row = self.create_debug_section(row)

    def create_title_section(self, row: int) -> int:
        """Create the title section."""
        title_frame = ctk.CTkFrame(self.main_frame)
        title_frame.grid(row=row, column=0, pady=(0, 10), sticky="ew")
        title_frame.grid_columnconfigure(0, weight=1)

        title_label = ctk.CTkLabel(
            title_frame,
            text=f"🔨 Advanced Mining Bot v{VERSION}",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, pady=10)

        subtitle_label = ctk.CTkLabel(
            title_frame,
            text="Modern mining automation with comprehensive features",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        subtitle_label.grid(row=1, column=0, pady=(0, 10))

        return row + 1

    def create_status_section(self, row: int) -> int:
        """Create the real-time status section."""
        status_frame = ctk.CTkFrame(self.main_frame)
        status_frame.grid(row=row, column=0, pady=(0, 10), sticky="ew")
        status_frame.grid_columnconfigure(1, weight=1)

        # Section title
        status_title = ctk.CTkLabel(
            status_frame,
            text="📊 System Status",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        status_title.grid(row=0, column=0, columnspan=2, pady=(10, 5), sticky="w")

        # Status indicators
        ctk.CTkLabel(status_frame, text="State:").grid(row=1, column=0, padx=(10, 5), pady=2, sticky="w")
        self.status_state_label = ctk.CTkLabel(status_frame, text="Stopped", text_color="red")
        self.status_state_label.grid(row=1, column=1, padx=(5, 10), pady=2, sticky="w")

        ctk.CTkLabel(status_frame, text="Action:").grid(row=2, column=0, padx=(10, 5), pady=2, sticky="w")
        self.status_action_label = ctk.CTkLabel(status_frame, text="Idle")
        self.status_action_label.grid(row=2, column=1, padx=(5, 10), pady=2, sticky="w")

        ctk.CTkLabel(status_frame, text="Runtime:").grid(row=3, column=0, padx=(10, 5), pady=2, sticky="w")
        self.status_runtime_label = ctk.CTkLabel(status_frame, text="00:00:00")
        self.status_runtime_label.grid(row=3, column=1, padx=(5, 10), pady=2, sticky="w")

        ctk.CTkLabel(status_frame, text="Profile:").grid(row=4, column=0, padx=(10, 5), pady=(2, 10), sticky="w")
        self.status_profile_label = ctk.CTkLabel(status_frame, text=self.current_profile.name)
        self.status_profile_label.grid(row=4, column=1, padx=(5, 10), pady=(2, 10), sticky="w")

        return row + 1

    def create_profile_section(self, row: int) -> int:
        """Create the profile selection section."""
        profile_frame = ctk.CTkFrame(self.main_frame)
        profile_frame.grid(row=row, column=0, pady=(0, 10), sticky="ew")
        profile_frame.grid_columnconfigure(1, weight=1)

        # Section title
        profile_title = ctk.CTkLabel(
            profile_frame,
            text="⚙️ Mining Profile",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        profile_title.grid(row=0, column=0, columnspan=3, pady=(10, 5), sticky="w")

        # Profile selection
        ctk.CTkLabel(profile_frame, text="Active Profile:").grid(row=1, column=0, padx=(10, 5), pady=5, sticky="w")

        self.profile_var = ctk.StringVar(value=self.config_manager.current_profile)
        self.profile_dropdown = ctk.CTkOptionMenu(
            profile_frame,
            variable=self.profile_var,
            values=list(self.config_manager.profiles.keys()),
            command=self.on_profile_change
        )
        self.profile_dropdown.grid(row=1, column=1, padx=5, pady=5, sticky="ew")

        # Profile management buttons
        self.new_profile_button = ctk.CTkButton(
            profile_frame,
            text="New",
            width=60,
            command=self.create_new_profile
        )
        self.new_profile_button.grid(row=1, column=2, padx=(5, 10), pady=5)

        # Profile description
        self.profile_desc_label = ctk.CTkLabel(
            profile_frame,
            text=self.current_profile.description,
            wraplength=500,
            justify="left"
        )
        self.profile_desc_label.grid(row=2, column=0, columnspan=3, padx=10, pady=(0, 10), sticky="ew")

        return row + 1

    def create_mining_control_section(self, row: int) -> int:
        """Create the main mining control section."""
        control_frame = ctk.CTkFrame(self.main_frame)
        control_frame.grid(row=row, column=0, pady=(0, 10), sticky="ew")
        control_frame.grid_columnconfigure(0, weight=1)

        # Section title
        control_title = ctk.CTkLabel(
            control_frame,
            text="🎮 Mining Control",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        control_title.grid(row=0, column=0, pady=(10, 10))

        # Main control buttons
        button_frame = ctk.CTkFrame(control_frame)
        button_frame.grid(row=1, column=0, padx=10, pady=(0, 10), sticky="ew")
        button_frame.grid_columnconfigure((0, 1, 2), weight=1)

        self.start_button = ctk.CTkButton(
            button_frame,
            text="▶️ Start Mining",
            command=self.start_mining,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.start_button.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

        self.pause_button = ctk.CTkButton(
            button_frame,
            text="⏸️ Pause",
            command=self.pause_mining,
            height=40,
            state="disabled"
        )
        self.pause_button.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

        self.stop_button = ctk.CTkButton(
            button_frame,
            text="⏹️ Stop",
            command=self.stop_mining,
            height=40,
            state="disabled"
        )
        self.stop_button.grid(row=0, column=2, padx=5, pady=5, sticky="ew")

        # Emergency stop
        self.emergency_button = ctk.CTkButton(
            control_frame,
            text="🚨 EMERGENCY STOP",
            command=self.emergency_stop,
            height=35,
            fg_color="red",
            hover_color="darkred"
        )
        self.emergency_button.grid(row=2, column=0, padx=10, pady=(0, 10), sticky="ew")

        return row + 1

    def create_movement_section(self, row: int) -> int:
        """Create the movement configuration section."""
        movement_frame = ctk.CTkFrame(self.main_frame)
        movement_frame.grid(row=row, column=0, pady=(0, 10), sticky="ew")
        movement_frame.grid_columnconfigure(1, weight=1)

        # Section title
        movement_title = ctk.CTkLabel(
            movement_frame,
            text="🏃 Movement Settings",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        movement_title.grid(row=0, column=0, columnspan=2, pady=(10, 10), sticky="w")

        # Movement pattern
        ctk.CTkLabel(movement_frame, text="Pattern:").grid(row=1, column=0, padx=(10, 5), pady=5, sticky="w")
        # Handle both enum and string values
        pattern_value = self.current_profile.movement.pattern
        if hasattr(pattern_value, 'value'):
            pattern_str = pattern_value.value
        else:
            pattern_str = str(pattern_value)

        self.pattern_var = ctk.StringVar(value=pattern_str)
        self.pattern_dropdown = ctk.CTkOptionMenu(
            movement_frame,
            variable=self.pattern_var,
            values=[p.value for p in MovementPattern],
            command=self.on_pattern_change
        )
        self.pattern_dropdown.grid(row=1, column=1, padx=(5, 10), pady=5, sticky="ew")

        # Speed setting
        ctk.CTkLabel(movement_frame, text="Speed:").grid(row=2, column=0, padx=(10, 5), pady=5, sticky="w")
        self.speed_var = ctk.DoubleVar(value=self.current_profile.movement.speed)
        self.speed_slider = ctk.CTkSlider(
            movement_frame,
            from_=0.001,
            to=0.02,
            variable=self.speed_var,
            command=self.on_speed_change
        )
        self.speed_slider.grid(row=2, column=1, padx=(5, 10), pady=5, sticky="ew")

        # Speed label
        self.speed_label = ctk.CTkLabel(movement_frame, text=f"{self.speed_var.get():.3f}")
        self.speed_label.grid(row=3, column=1, padx=(5, 10), pady=(0, 5), sticky="w")

        # Steps setting
        ctk.CTkLabel(movement_frame, text="Steps:").grid(row=4, column=0, padx=(10, 5), pady=5, sticky="w")
        self.steps_var = ctk.IntVar(value=self.current_profile.movement.steps)
        self.steps_slider = ctk.CTkSlider(
            movement_frame,
            from_=5,
            to=30,
            number_of_steps=25,
            variable=self.steps_var,
            command=self.on_steps_change
        )
        self.steps_slider.grid(row=4, column=1, padx=(5, 10), pady=5, sticky="ew")

        # Steps label
        self.steps_label = ctk.CTkLabel(movement_frame, text=str(self.steps_var.get()))
        self.steps_label.grid(row=5, column=1, padx=(5, 10), pady=(0, 5), sticky="w")

        # Human-like behavior toggles
        ctk.CTkLabel(movement_frame, text="Human-like Features:",
                    font=ctk.CTkFont(weight="bold")).grid(row=6, column=0, columnspan=2, padx=(10, 5), pady=(10, 5), sticky="w")

        # Smooth transitions toggle
        self.smooth_transitions_var = ctk.BooleanVar(value=self.current_profile.movement.smooth_transitions)
        self.smooth_transitions_check = ctk.CTkCheckBox(
            movement_frame,
            text="Smooth Transitions",
            variable=self.smooth_transitions_var,
            command=self.on_smooth_transitions_change
        )
        self.smooth_transitions_check.grid(row=7, column=0, columnspan=2, padx=(10, 5), pady=2, sticky="w")

        # Variable timing toggle
        self.variable_timing_var = ctk.BooleanVar(value=self.current_profile.movement.variable_timing)
        self.variable_timing_check = ctk.CTkCheckBox(
            movement_frame,
            text="Variable Timing",
            variable=self.variable_timing_var,
            command=self.on_variable_timing_change
        )
        self.variable_timing_check.grid(row=8, column=0, columnspan=2, padx=(10, 5), pady=2, sticky="w")

        # Path deviation toggle
        self.path_deviation_var = ctk.BooleanVar(value=self.current_profile.movement.path_deviation)
        self.path_deviation_check = ctk.CTkCheckBox(
            movement_frame,
            text="Natural Path Variations",
            variable=self.path_deviation_var,
            command=self.on_path_deviation_change
        )
        self.path_deviation_check.grid(row=9, column=0, columnspan=2, padx=(10, 5), pady=2, sticky="w")

        # Micro corrections toggle
        self.micro_corrections_var = ctk.BooleanVar(value=self.current_profile.movement.micro_corrections)
        self.micro_corrections_check = ctk.CTkCheckBox(
            movement_frame,
            text="Micro Corrections",
            variable=self.micro_corrections_var,
            command=self.on_micro_corrections_change
        )
        self.micro_corrections_check.grid(row=10, column=0, columnspan=2, padx=(10, 5), pady=2, sticky="w")

        # Hesitation chance slider
        ctk.CTkLabel(movement_frame, text="Hesitation Chance:").grid(row=11, column=0, padx=(10, 5), pady=(10, 5), sticky="w")
        self.hesitation_var = ctk.DoubleVar(value=self.current_profile.movement.hesitation_chance)
        self.hesitation_slider = ctk.CTkSlider(
            movement_frame,
            from_=0.0,
            to=0.5,
            variable=self.hesitation_var,
            command=self.on_hesitation_change
        )
        self.hesitation_slider.grid(row=11, column=1, padx=(5, 10), pady=(10, 5), sticky="ew")

        # Hesitation label
        self.hesitation_label = ctk.CTkLabel(movement_frame, text=f"{self.hesitation_var.get():.2f}")
        self.hesitation_label.grid(row=12, column=1, padx=(5, 10), pady=(0, 5), sticky="w")

        # Prison Mining specific controls
        ctk.CTkLabel(movement_frame, text="Prison Mining Settings:",
                    font=ctk.CTkFont(weight="bold")).grid(row=13, column=0, columnspan=2, padx=(10, 5), pady=(10, 5), sticky="w")

        # Scanning range slider
        ctk.CTkLabel(movement_frame, text="Scanning Range:").grid(row=14, column=0, padx=(10, 5), pady=5, sticky="w")
        self.scanning_range_var = ctk.DoubleVar(value=getattr(self.current_profile.movement, 'scanning_range', 45.0))
        self.scanning_range_slider = ctk.CTkSlider(
            movement_frame,
            from_=20.0,
            to=90.0,
            variable=self.scanning_range_var,
            command=self.on_scanning_range_change
        )
        self.scanning_range_slider.grid(row=14, column=1, padx=(5, 10), pady=5, sticky="ew")

        # Scanning range label
        self.scanning_range_label = ctk.CTkLabel(movement_frame, text=f"{self.scanning_range_var.get():.1f}°")
        self.scanning_range_label.grid(row=15, column=1, padx=(5, 10), pady=(0, 5), sticky="w")

        # Scanning speed slider
        ctk.CTkLabel(movement_frame, text="Scanning Speed:").grid(row=16, column=0, padx=(10, 5), pady=5, sticky="w")
        self.scanning_speed_var = ctk.DoubleVar(value=getattr(self.current_profile.movement, 'scanning_speed', 0.8))
        self.scanning_speed_slider = ctk.CTkSlider(
            movement_frame,
            from_=0.2,
            to=2.0,
            variable=self.scanning_speed_var,
            command=self.on_scanning_speed_change
        )
        self.scanning_speed_slider.grid(row=16, column=1, padx=(5, 10), pady=5, sticky="ew")

        # Scanning speed label
        self.scanning_speed_label = ctk.CTkLabel(movement_frame, text=f"{self.scanning_speed_var.get():.1f}")
        self.scanning_speed_label.grid(row=17, column=1, padx=(5, 10), pady=(0, 10), sticky="w")

        return row + 1

    def create_safety_section(self, row: int) -> int:
        """Create the safety features section."""
        safety_frame = ctk.CTkFrame(self.main_frame)
        safety_frame.grid(row=row, column=0, pady=(0, 10), sticky="ew")
        safety_frame.grid_columnconfigure(1, weight=1)

        # Section title
        safety_title = ctk.CTkLabel(
            safety_frame,
            text="🛡️ Safety Features",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        safety_title.grid(row=0, column=0, columnspan=2, pady=(10, 10), sticky="w")

        # Boundary detection
        self.boundary_var = ctk.BooleanVar(value=self.current_profile.safety.enable_boundaries)
        self.boundary_check = ctk.CTkCheckBox(
            safety_frame,
            text="Enable Boundary Detection",
            variable=self.boundary_var,
            command=self.on_boundary_toggle
        )
        self.boundary_check.grid(row=1, column=0, columnspan=2, padx=10, pady=5, sticky="w")

        # Stuck detection time
        ctk.CTkLabel(safety_frame, text="Stuck Detection (s):").grid(row=2, column=0, padx=(10, 5), pady=5, sticky="w")
        self.stuck_var = ctk.DoubleVar(value=self.current_profile.safety.stuck_detection_time)
        self.stuck_slider = ctk.CTkSlider(
            safety_frame,
            from_=1.0,
            to=10.0,
            variable=self.stuck_var,
            command=self.on_stuck_change
        )
        self.stuck_slider.grid(row=2, column=1, padx=(5, 10), pady=5, sticky="ew")

        # Audio alarms
        self.audio_var = ctk.BooleanVar(value=self.current_profile.safety.enable_audio_alarms)
        self.audio_check = ctk.CTkCheckBox(
            safety_frame,
            text="Enable Audio Alarms",
            variable=self.audio_var,
            command=self.on_audio_toggle
        )
        self.audio_check.grid(row=3, column=0, columnspan=2, padx=10, pady=(5, 10), sticky="w")

        return row + 1

    def create_selling_section(self, row: int) -> int:
        """Create the auto-selling section."""
        selling_frame = ctk.CTkFrame(self.main_frame)
        selling_frame.grid(row=row, column=0, pady=(0, 10), sticky="ew")
        selling_frame.grid_columnconfigure(1, weight=1)

        # Section title
        selling_title = ctk.CTkLabel(
            selling_frame,
            text="💰 Auto-Selling",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        selling_title.grid(row=0, column=0, columnspan=2, pady=(10, 10), sticky="w")

        # Enable selling
        self.selling_var = ctk.BooleanVar(value=self.current_profile.selling.enabled)
        self.selling_check = ctk.CTkCheckBox(
            selling_frame,
            text="Enable Auto-Selling",
            variable=self.selling_var,
            command=self.on_selling_toggle
        )
        self.selling_check.grid(row=1, column=0, columnspan=2, padx=10, pady=5, sticky="w")

        # Trigger type
        ctk.CTkLabel(selling_frame, text="Trigger:").grid(row=2, column=0, padx=(10, 5), pady=5, sticky="w")
        # Handle both enum and string values
        trigger_value = self.current_profile.selling.trigger_type
        if hasattr(trigger_value, 'value'):
            trigger_str = trigger_value.value
        else:
            trigger_str = str(trigger_value)

        self.trigger_var = ctk.StringVar(value=trigger_str)
        self.trigger_dropdown = ctk.CTkOptionMenu(
            selling_frame,
            variable=self.trigger_var,
            values=[t.value for t in SellTrigger],
            command=self.on_trigger_change
        )
        self.trigger_dropdown.grid(row=2, column=1, padx=(5, 10), pady=5, sticky="ew")

        # Cooldown
        ctk.CTkLabel(selling_frame, text="Cooldown (s):").grid(row=3, column=0, padx=(10, 5), pady=5, sticky="w")
        self.cooldown_var = ctk.DoubleVar(value=self.current_profile.selling.cooldown_seconds)
        self.cooldown_slider = ctk.CTkSlider(
            selling_frame,
            from_=1.0,
            to=30.0,
            variable=self.cooldown_var,
            command=self.on_cooldown_change
        )
        self.cooldown_slider.grid(row=3, column=1, padx=(5, 10), pady=(5, 10), sticky="ew")

        return row + 1

    def create_statistics_section(self, row: int) -> int:
        """Create the statistics section."""
        stats_frame = ctk.CTkFrame(self.main_frame)
        stats_frame.grid(row=row, column=0, pady=(0, 10), sticky="ew")
        stats_frame.grid_columnconfigure((0, 1), weight=1)

        # Section title
        stats_title = ctk.CTkLabel(
            stats_frame,
            text="📈 Statistics",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        stats_title.grid(row=0, column=0, columnspan=2, pady=(10, 10))

        # Session stats
        session_frame = ctk.CTkFrame(stats_frame)
        session_frame.grid(row=1, column=0, padx=(10, 5), pady=(0, 10), sticky="ew")

        ctk.CTkLabel(session_frame, text="Session Stats", font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, pady=5)

        self.session_blocks_label = ctk.CTkLabel(session_frame, text="Blocks: 0")
        self.session_blocks_label.grid(row=1, column=0, pady=2)

        self.session_resets_label = ctk.CTkLabel(session_frame, text="Resets: 0")
        self.session_resets_label.grid(row=2, column=0, pady=2)

        self.session_sells_label = ctk.CTkLabel(session_frame, text="Items Sold: 0")
        self.session_sells_label.grid(row=3, column=0, pady=(2, 10))

        # Overall stats
        overall_frame = ctk.CTkFrame(stats_frame)
        overall_frame.grid(row=1, column=1, padx=(5, 10), pady=(0, 10), sticky="ew")

        ctk.CTkLabel(overall_frame, text="Overall Stats", font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, pady=5)

        self.overall_blocks_label = ctk.CTkLabel(overall_frame, text=f"Total Blocks: {self.config_manager.overall_stats.total_blocks_mined}")
        self.overall_blocks_label.grid(row=1, column=0, pady=2)

        self.overall_sessions_label = ctk.CTkLabel(overall_frame, text=f"Sessions: {self.config_manager.overall_stats.sessions_completed}")
        self.overall_sessions_label.grid(row=2, column=0, pady=2)

        self.overall_runtime_label = ctk.CTkLabel(overall_frame, text=f"Runtime: {self.config_manager.overall_stats.total_runtime:.1f}h")
        self.overall_runtime_label.grid(row=3, column=0, pady=(2, 10))

        return row + 1

    # Event handlers for settings changes
    def on_pattern_change(self, pattern: str):
        """Handle movement pattern change."""
        self.current_profile.movement.pattern = MovementPattern(pattern)
        self.config_manager.save_profiles()
        self.log_debug(f"Movement pattern changed to: {pattern}")

    def on_speed_change(self, value):
        """Handle speed change."""
        self.current_profile.movement.speed = float(value)
        self.speed_label.configure(text=f"{float(value):.3f}")
        self.config_manager.save_profiles()

    def on_steps_change(self, value):
        """Handle steps change."""
        self.current_profile.movement.steps = int(value)
        self.steps_label.configure(text=str(int(value)))
        self.config_manager.save_profiles()

    def on_smooth_transitions_change(self):
        """Handle smooth transitions toggle."""
        self.current_profile.movement.smooth_transitions = self.smooth_transitions_var.get()
        self.config_manager.save_profiles()
        self.log_debug(f"Smooth transitions: {'enabled' if self.smooth_transitions_var.get() else 'disabled'}")

    def on_variable_timing_change(self):
        """Handle variable timing toggle."""
        self.current_profile.movement.variable_timing = self.variable_timing_var.get()
        self.config_manager.save_profiles()
        self.log_debug(f"Variable timing: {'enabled' if self.variable_timing_var.get() else 'disabled'}")

    def on_path_deviation_change(self):
        """Handle path deviation toggle."""
        self.current_profile.movement.path_deviation = self.path_deviation_var.get()
        self.config_manager.save_profiles()
        self.log_debug(f"Path deviation: {'enabled' if self.path_deviation_var.get() else 'disabled'}")

    def on_micro_corrections_change(self):
        """Handle micro corrections toggle."""
        self.current_profile.movement.micro_corrections = self.micro_corrections_var.get()
        self.config_manager.save_profiles()
        self.log_debug(f"Micro corrections: {'enabled' if self.micro_corrections_var.get() else 'disabled'}")

    def on_hesitation_change(self, value):
        """Handle hesitation chance change."""
        self.current_profile.movement.hesitation_chance = float(value)
        self.hesitation_label.configure(text=f"{float(value):.2f}")
        self.config_manager.save_profiles()
        self.log_debug(f"Hesitation chance set to: {float(value):.2f}")

    def on_scanning_range_change(self, value):
        """Handle scanning range change for prison mining."""
        self.scanning_range_label.configure(text=f"{value:.1f}°")
        if hasattr(self.current_profile, 'movement'):
            self.current_profile.movement.scanning_range = value
            self.config_manager.save_profiles()
            self.log_debug(f"Prison mining scanning range set to: {value:.1f}°")

    def on_scanning_speed_change(self, value):
        """Handle scanning speed change for prison mining."""
        self.scanning_speed_label.configure(text=f"{value:.1f}")
        if hasattr(self.current_profile, 'movement'):
            self.current_profile.movement.scanning_speed = value
            self.config_manager.save_profiles()
            self.log_debug(f"Prison mining scanning speed set to: {value:.1f}")

    def on_boundary_toggle(self):
        """Handle boundary detection toggle."""
        self.current_profile.safety.enable_boundaries = self.boundary_var.get()
        self.config_manager.save_profiles()

    def on_stuck_change(self, value):
        """Handle stuck detection time change."""
        self.current_profile.safety.stuck_detection_time = float(value)
        self.config_manager.save_profiles()

    def on_audio_toggle(self):
        """Handle audio alarms toggle."""
        self.current_profile.safety.enable_audio_alarms = self.audio_var.get()
        self.config_manager.save_profiles()

    def on_selling_toggle(self):
        """Handle auto-selling toggle."""
        self.current_profile.selling.enabled = self.selling_var.get()
        self.config_manager.save_profiles()

    def on_trigger_change(self, trigger: str):
        """Handle selling trigger change."""
        self.current_profile.selling.trigger_type = SellTrigger(trigger)
        self.config_manager.save_profiles()

    def on_cooldown_change(self, value):
        """Handle selling cooldown change."""
        self.current_profile.selling.cooldown_seconds = float(value)
        self.config_manager.save_profiles()

    def create_chat_section(self, row: int) -> int:
        """Create the chat/command section."""
        chat_frame = ctk.CTkFrame(self.main_frame)
        chat_frame.grid(row=row, column=0, pady=(0, 10), sticky="ew")
        chat_frame.grid_columnconfigure(0, weight=1)

        # Section title
        chat_title = ctk.CTkLabel(
            chat_frame,
            text="💬 Chat & Commands",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        chat_title.grid(row=0, column=0, pady=(10, 5), sticky="w")

        # Chat entry
        self.chat_entry = ctk.CTkEntry(
            chat_frame,
            placeholder_text="Enter message or command..."
        )
        self.chat_entry.grid(row=1, column=0, padx=10, pady=5, sticky="ew")
        self.chat_entry.bind("<Return>", self.send_chat)

        # Send button
        self.send_button = ctk.CTkButton(
            chat_frame,
            text="Send",
            command=self.send_chat,
            width=80
        )
        self.send_button.grid(row=2, column=0, padx=10, pady=(0, 10), sticky="e")

        return row + 1

    def create_debug_section(self, row: int) -> int:
        """Create the debug output section."""
        debug_frame = ctk.CTkFrame(self.main_frame)
        debug_frame.grid(row=row, column=0, pady=(0, 10), sticky="ew")
        debug_frame.grid_columnconfigure(0, weight=1)

        # Section title
        debug_title = ctk.CTkLabel(
            debug_frame,
            text="🐛 Debug Output",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        debug_title.grid(row=0, column=0, pady=(10, 5), sticky="w")

        # Debug text area
        self.debug_text = ctk.CTkTextbox(
            debug_frame,
            height=150,
            state="disabled"
        )
        self.debug_text.grid(row=1, column=0, padx=10, pady=(0, 10), sticky="ew")

        return row + 1

    # Event handlers
    def on_profile_change(self, profile_name: str):
        """Handle profile change."""
        self.config_manager.set_current_profile(profile_name)
        self.current_profile = self.config_manager.get_current_profile()
        self.movement_controller.update_profile(self.current_profile)
        self.selling_system.update_profile(self.current_profile)

        # Update GUI
        self.status_profile_label.configure(text=self.current_profile.name)
        self.profile_desc_label.configure(text=self.current_profile.description)

        # Update prison mining controls
        if hasattr(self, 'scanning_range_var'):
            self.scanning_range_var.set(getattr(self.current_profile.movement, 'scanning_range', 45.0))
            self.scanning_range_label.configure(text=f"{self.scanning_range_var.get():.1f}°")

        if hasattr(self, 'scanning_speed_var'):
            self.scanning_speed_var.set(getattr(self.current_profile.movement, 'scanning_speed', 0.8))
            self.scanning_speed_label.configure(text=f"{self.scanning_speed_var.get():.1f}")

        self.log_debug(f"Switched to profile: {profile_name}")

    def create_new_profile(self):
        """Create a new mining profile."""
        # Simple dialog for profile name
        dialog = ctk.CTkInputDialog(text="Enter profile name:", title="New Profile")
        name = dialog.get_input()

        if name and name.strip():
            if self.config_manager.create_profile(name.strip()):
                # Update dropdown
                self.profile_dropdown.configure(values=list(self.config_manager.profiles.keys()))
                self.profile_var.set(name.strip())
                self.on_profile_change(name.strip())
                self.log_debug(f"Created new profile: {name.strip()}")
            else:
                self.log_debug(f"Failed to create profile: {name.strip()} (already exists)")

    def start_mining(self):
        """Start the mining operation."""
        if not self.running:
            self.running = True
            self.state_manager.set_state(MiningState.STARTING, "Initializing mining...")

            # Update button states
            self.start_button.configure(state="disabled")
            self.pause_button.configure(state="normal")
            self.stop_button.configure(state="normal")

            # Start mining thread
            self.script_thread = threading.Thread(target=self.mining_loop, daemon=True)
            self.script_thread.start()

            self.log_debug("Mining started")

    def pause_mining(self):
        """Pause/resume mining operation."""
        current_state = self.state_manager.get_state()
        if current_state == MiningState.PAUSED:
            self.state_manager.set_state(MiningState.MINING, "Resuming mining...")
            self.pause_button.configure(text="⏸️ Pause")
            self.log_debug("Mining resumed")
        else:
            self.state_manager.set_state(MiningState.PAUSED, "Mining paused")
            self.pause_button.configure(text="▶️ Resume")
            self.log_debug("Mining paused")

    def stop_mining(self):
        """Stop the mining operation."""
        self.running = False
        self.state_manager.set_state(MiningState.STOPPED, "Stopping...")

        # Update button states
        self.start_button.configure(state="normal")
        self.pause_button.configure(state="disabled", text="⏸️ Pause")
        self.stop_button.configure(state="disabled")

        # Stop all movements
        self.stop_all_movements()

        self.log_debug("Mining stopped")

    def emergency_stop(self):
        """Emergency stop with immediate halt."""
        self.running = False
        self.state_manager.set_state(MiningState.STOPPED, "Emergency stop activated")

        # Immediate movement stop
        self.stop_all_movements()

        # Update GUI
        self.start_button.configure(state="normal")
        self.pause_button.configure(state="disabled", text="⏸️ Pause")
        self.stop_button.configure(state="disabled")

        self.log_debug("🚨 EMERGENCY STOP ACTIVATED")

    def send_chat(self, event=None):
        """Send chat message or execute command."""
        message = self.chat_entry.get().strip()
        if message:
            if message.startswith("/"):
                execute(message)
                self.log_debug(f"Command executed: {message}")
            else:
                chat(message)
                self.log_debug(f"Chat sent: {message}")

            self.chat_entry.delete(0, 'end')

    def log_debug(self, message: str):
        """Add message to debug output."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.debug_text.configure(state="normal")
        self.debug_text.insert("end", formatted_message)
        self.debug_text.see("end")
        self.debug_text.configure(state="disabled")

    def stop_all_movements(self):
        """Stop all player movements."""
        try:
            player_press_forward(False)
            player_press_attack(False)
            player_press_left(False)
            player_press_right(False)
            player_press_jump(False)
        except Exception as e:
            self.log_debug(f"Error stopping movements: {e}")

    def mining_loop(self):
        """Main mining loop with modern features."""
        try:
            self.config_manager.session_stats = SessionStats()  # Reset session stats
            self.state_manager.set_state(MiningState.MINING, "Mining in progress...")

            while self.running:
                current_state = self.state_manager.get_state()

                # Handle paused state
                if current_state == MiningState.PAUSED:
                    time.sleep(0.1)
                    continue

                # Check for stuck detection
                if self.movement_controller.check_stuck_detection():
                    self.log_debug("🚨 Stuck detection triggered!")
                    if self.current_profile.safety.enable_audio_alarms:
                        # Could add audio alarm here
                        pass
                    self.emergency_stop()
                    break

                # Check selling trigger
                if self.selling_system.should_sell():
                    self.state_manager.set_state(MiningState.SELLING, "Auto-selling items...")
                    if self.selling_system.execute_sell():
                        self.config_manager.session_stats.items_sold += 1
                    time.sleep(1)  # Brief pause after selling

                # Execute movement sequence
                self.execute_movement_sequence()

                # Check for mine reset
                if self.check_mine_reset():
                    self.state_manager.set_state(MiningState.RESETTING, "Resetting mine...")
                    self.execute_mine_reset()
                    self.config_manager.session_stats.resets_performed += 1

                # Update statistics
                self.config_manager.session_stats.blocks_mined += 1

                # Small delay to prevent overwhelming the system
                time.sleep(0.001)

        except Exception as e:
            self.log_debug(f"Error in mining loop: {e}")
            self.state_manager.set_error(str(e))
        finally:
            self.stop_mining()

    def execute_movement_sequence(self):
        """Execute the movement sequence with advanced behavior patterns."""
        try:
            sequence = self.movement_controller.get_smooth_cursor_sequence()

            for movement in sequence:
                if not self.running or self.state_manager.get_state() == MiningState.PAUSED:
                    break

                # Handle different movement types
                movement_type = movement.get("type", "standard")

                if movement_type == "prison_mining":
                    self._execute_prison_mining(movement)
                elif movement_type == "hesitation":
                    self._execute_hesitation(movement)
                else:
                    # Standard human-like movement
                    self._execute_human_like_movement(movement)

        except Exception as e:
            self.log_debug(f"Error in movement sequence: {e}")

    def _execute_prison_mining(self, movement: Dict):
        """Execute authentic prison mining with continuous forward movement and left-right scanning."""
        try:
            # Start continuous forward movement and attacking - NEVER STOP
            player_press_forward(True)
            player_press_attack(True)

            # Get scanning parameters
            scanning_range = movement.get("scanning_range", 45.0)
            scanning_speed = movement.get("scanning_speed", 0.8)
            scanning_smoothness = movement.get("scanning_smoothness", 20)
            base_delay = movement.get("base_delay", 0.002)

            # Get current orientation as center point
            center_yaw, center_pitch = player_orientation()

            # Calculate scanning boundaries
            left_boundary = center_yaw - scanning_range
            right_boundary = center_yaw + scanning_range

            # Prison mining continuous scanning loop
            scanning_direction = 1  # 1 for right, -1 for left
            current_yaw = center_yaw

            # Execute continuous scanning for a duration
            scan_duration = random.uniform(3.0, 8.0)  # Random scan duration
            start_time = time.time()

            while (time.time() - start_time) < scan_duration and self.running:
                if self.state_manager.get_state() == MiningState.PAUSED:
                    break

                # Calculate smooth yaw increment for seamless movement
                yaw_increment = (scanning_speed * scanning_direction) / scanning_smoothness
                current_yaw += yaw_increment

                # Seamless direction reversal at boundaries
                if current_yaw >= right_boundary:
                    scanning_direction = -1
                    current_yaw = right_boundary
                elif current_yaw <= left_boundary:
                    scanning_direction = 1
                    current_yaw = left_boundary

                # Add slight pitch variation for tunnel focus
                pitch_variation = random.uniform(-2.0, 2.0)
                target_pitch = center_pitch + pitch_variation

                # Set orientation smoothly
                player_set_orientation(current_yaw, target_pitch)

                # Maintain continuous forward movement and attacking
                player_press_forward(True)
                player_press_attack(True)

                # Very small delay for smooth movement
                time.sleep(base_delay)

            # Continue forward movement and attacking (never stop in prison mining)
            # The movement will be maintained until the next sequence

        except Exception as e:
            self.log_debug(f"Error in prison mining execution: {e}")
            # Ensure movement continues even if there's an error
            try:
                player_press_forward(True)
                player_press_attack(True)
            except:
                pass

    def _execute_hesitation(self, movement: Dict):
        """Execute a hesitation pause with subtle micro-movements."""
        hesitation_time = movement["duration"]

        # During hesitation, add very subtle movements to mimic human uncertainty
        start_time = time.time()
        while time.time() - start_time < hesitation_time:
            if not self.running:
                break

            # Very small random adjustments during hesitation
            if random.random() < 0.3:  # 30% chance of micro-adjustment
                current_yaw, current_pitch = player_orientation()
                micro_yaw = current_yaw + random.uniform(-0.5, 0.5)
                micro_pitch = current_pitch + random.uniform(-0.2, 0.2)
                player_set_orientation(micro_yaw, micro_pitch)

            time.sleep(0.05)  # Small sleep during hesitation

    def _execute_human_like_movement(self, movement: Dict):
        """Execute a single movement with all human-like characteristics."""
        # Human reaction delay before starting movement
        if "reaction_delay" in movement:
            time.sleep(movement["reaction_delay"])
            if not self.running:
                return

        # Get current orientation
        current_yaw, current_pitch = player_orientation()
        target_yaw = current_yaw + movement["yaw_offset"]
        target_pitch = current_pitch + movement["pitch_offset"]

        # Smooth transition to target orientation
        if movement.get("smooth_transition", False):
            self._smooth_orientation_transition(current_yaw, current_pitch, target_yaw, target_pitch,
                                              movement.get("transition_steps", 3))
        else:
            player_set_orientation(target_yaw, target_pitch)

        # Execute movement with variable timing
        self._execute_movement_steps(movement)

        # Micro-correction after movement if needed
        if movement.get("needs_correction", False):
            self._apply_micro_correction()

    def _smooth_orientation_transition(self, start_yaw: float, start_pitch: float,
                                     target_yaw: float, target_pitch: float, steps: int):
        """Smoothly transition from current to target orientation."""
        yaw_step = (target_yaw - start_yaw) / steps
        pitch_step = (target_pitch - start_pitch) / steps

        for i in range(steps):
            if not self.running:
                break

            # Calculate intermediate position with slight randomization
            progress = (i + 1) / steps
            current_yaw = start_yaw + (yaw_step * (i + 1))
            current_pitch = start_pitch + (pitch_step * (i + 1))

            # Add slight imperfection to the transition
            current_yaw += random.uniform(-0.3, 0.3) * (1 - progress)  # Less randomness as we approach target
            current_pitch += random.uniform(-0.15, 0.15) * (1 - progress)

            player_set_orientation(current_yaw, current_pitch)
            time.sleep(random.uniform(0.02, 0.05))  # Small delay between transition steps

    def _execute_movement_steps(self, movement: Dict):
        """Execute movement steps with human-like timing variations."""
        steps = movement["steps"]
        base_delay = movement["delay"]

        # Start movement
        player_press_forward(True)
        player_press_attack(True)

        for step in range(steps):
            if not self.running:
                break

            # Variable delay for each step (humans don't move at constant speed)
            step_delay = base_delay * random.uniform(0.7, 1.3)

            # Occasionally pause slightly longer (human inconsistency)
            if random.random() < 0.1:  # 10% chance
                step_delay *= random.uniform(1.2, 1.8)

            time.sleep(step_delay)

        # Stop movement with slight delay (humans don't stop instantly)
        time.sleep(random.uniform(0.01, 0.03))
        player_press_forward(False)
        player_press_attack(False)

    def _apply_micro_correction(self):
        """Apply a small correction movement to mimic human adjustment."""
        current_yaw, current_pitch = player_orientation()

        # Small correction movement
        correction_yaw = current_yaw + random.uniform(-2, 2)
        correction_pitch = current_pitch + random.uniform(-1, 1)

        player_set_orientation(correction_yaw, correction_pitch)
        time.sleep(random.uniform(0.05, 0.15))  # Brief pause after correction

    def check_mine_reset(self) -> bool:
        """Check if mine reset is needed."""
        if not self.current_profile.reset.enabled:
            return False

        try:
            # Check for reset trigger blocks
            targeted_block = player_get_targeted_block()
            if targeted_block and targeted_block.id in self.current_profile.reset.detection_blocks:
                return True
        except Exception as e:
            self.log_debug(f"Error checking mine reset: {e}")

        return False

    def execute_mine_reset(self):
        """Execute mine reset commands."""
        try:
            for command in self.current_profile.reset.reset_commands:
                execute(command)
                time.sleep(self.current_profile.reset.reset_delay)

            self.log_debug("Mine reset completed")

        except Exception as e:
            self.log_debug(f"Error executing mine reset: {e}")

    def setup_status_updates(self):
        """Set up real-time status updates."""
        def update_status():
            while True:
                try:
                    if hasattr(self, 'status_state_label'):
                        # Update state
                        state = self.state_manager.get_state()
                        state_text = state.value.title()

                        # Color coding
                        if state == MiningState.MINING:
                            color = "green"
                        elif state == MiningState.PAUSED:
                            color = "orange"
                        elif state == MiningState.ERROR:
                            color = "red"
                        else:
                            color = "gray"

                        self.status_state_label.configure(text=state_text, text_color=color)

                        # Update action
                        action = self.state_manager.get_current_action()
                        self.status_action_label.configure(text=action)

                        # Update runtime
                        if hasattr(self.config_manager.session_stats, 'start_time'):
                            runtime = datetime.now() - self.config_manager.session_stats.start_time
                            runtime_str = str(runtime).split('.')[0]  # Remove microseconds
                            self.status_runtime_label.configure(text=runtime_str)

                    time.sleep(1)  # Update every second

                except Exception as e:
                    print(f"Error updating status: {e}")
                    time.sleep(5)  # Wait longer on error

        self.update_thread = threading.Thread(target=update_status, daemon=True)
        self.update_thread.start()

    def on_closing(self):
        """Handle window closing."""
        self.running = False
        self.stop_all_movements()

        # Save configuration and statistics
        self.config_manager.save_profiles()
        self.config_manager.save_stats()

        self.destroy()

# Main execution
def main():
    """Main function to run the modern mining GUI."""
    try:
        # Set CustomTkinter appearance
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")

        # Create and run GUI
        app = ModernMiningGUI()
        app.mainloop()

    except Exception as e:
        echo(f"§c[Error] Failed to start GUI: {e}")
        traceback.print_exc()

# Initialize global managers
STATE_MANAGER = MiningStateManager()
CONFIG_MANAGER = ConfigManager()

# Run the application
if __name__ == "__main__":
    import sys

    # Check for command line arguments
    if len(sys.argv) > 1 and sys.argv[1] == "--reset-config":
        echo("§e[Config] Resetting configuration to defaults...")
        CONFIG_MANAGER.reset_configuration()
        echo("§a[Config] Configuration reset complete. You can now run the script normally.")
    else:
        main()
