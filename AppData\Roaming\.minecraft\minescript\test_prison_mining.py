#!/usr/bin/env python3
"""
Test script for prison mining functionality in ah_modern.py
This script tests the new prison mining pattern to ensure it works correctly.
"""

import sys
import os
import time
from unittest.mock import MagicMock

# Mock minescript module before importing ah_modern
sys.modules['minescript'] = MagicMock()

# Add the current directory to the path so we can import ah_modern
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_prison_mining_pattern():
    """Test the prison mining pattern configuration."""
    try:
        from ah_modern import MovementPattern, MovementConfig, MiningProfile
        
        print("Testing Prison Mining Pattern...")
        
        # Test enum
        assert MovementPattern.PRISON_MINING.value == "prison_mining"
        print("✓ Prison mining enum created successfully")
        
        # Test movement config with prison mining parameters
        config = MovementConfig(
            pattern=MovementPattern.PRISON_MINING,
            continuous_forward=True,
            scanning_range=45.0,
            scanning_speed=0.8,
            scanning_smoothness=20,
            tunnel_focus=True,
            seamless_transitions=True
        )
        
        assert config.pattern == MovementPattern.PRISON_MINING
        assert config.continuous_forward == True
        assert config.scanning_range == 45.0
        assert config.scanning_speed == 0.8
        print("✓ Prison mining configuration created successfully")
        
        # Test profile creation
        profile = MiningProfile(
            name="Test Prison Mining",
            description="Test profile for prison mining",
            movement=config
        )
        
        assert profile.movement.pattern == MovementPattern.PRISON_MINING
        print("✓ Prison mining profile created successfully")
        
        print("\n🎉 All prison mining tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_movement_controller():
    """Test the movement controller with prison mining."""
    try:
        from ah_modern import ModernMovementController, MiningProfile, MovementConfig, MovementPattern
        
        print("\nTesting Movement Controller...")
        
        # Create prison mining profile
        config = MovementConfig(
            pattern=MovementPattern.PRISON_MINING,
            continuous_forward=True,
            scanning_range=60.0,
            scanning_speed=1.0,
            scanning_smoothness=15
        )
        
        profile = MiningProfile(
            name="Test Controller",
            description="Test movement controller",
            movement=config
        )
        
        # Create movement controller
        controller = ModernMovementController(profile)
        
        # Test sequence generation
        sequence = controller.get_smooth_cursor_sequence()
        
        assert len(sequence) == 1  # Prison mining returns single continuous instruction
        assert sequence[0]["type"] == "prison_mining"
        assert sequence[0]["continuous_forward"] == True
        assert sequence[0]["scanning_range"] == 60.0
        
        print("✓ Movement controller generates correct prison mining sequence")
        print(f"✓ Sequence: {sequence[0]}")
        
        print("\n🎉 Movement controller tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Movement controller test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== Prison Mining Test Suite ===\n")
    
    success = True
    
    # Run tests
    success &= test_prison_mining_pattern()
    success &= test_movement_controller()
    
    if success:
        print("\n🎉 ALL TESTS PASSED! Prison mining is ready to use.")
        print("\nTo use prison mining:")
        print("1. Run ah_modern.py")
        print("2. Select 'Prison Mining' profile from the dropdown")
        print("3. Adjust scanning range and speed as needed")
        print("4. Start mining for continuous forward movement with left-right scanning")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        sys.exit(1)
