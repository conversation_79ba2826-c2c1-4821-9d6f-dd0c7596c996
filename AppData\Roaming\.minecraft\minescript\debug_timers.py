#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug version of timers.py to investigate Core event flashing issue
"""

import datetime
import pytz
import time
import os
import sys

# Set UTF-8 encoding for stdout to handle Unicode characters
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

# Configuration
DEFAULT_CUSTOMHUD_PATH = r"C:\Users\<USER>\AppData\Roaming\.minecraft\config\custom-hud\profiles\debug_timer.txt"
LOCAL_FALLBACK_PATH = "debug_timer.txt"

OUTPUT_FILE = DEFAULT_CUSTOMHUD_PATH
UPDATE_INTERVAL = 10  # Faster updates for debugging

# Event schedules
SKYBLOCK_OUTPOST_START = datetime.datetime(2025, 7, 29, 23, 0, 0)
SKYBLOCK_POND_START = datetime.datetime(2025, 7, 30, 2, 0, 0)
LIFESTEAL_OUTPOST_START = datetime.datetime(2025, 7, 30, 2, 0, 0)
LIFESTEAL_POND_START = datetime.datetime(2025, 7, 30, 1, 0, 0)
LIFESTEAL_CORE_START = datetime.datetime(2025, 7, 30, 23, 30, 0)

PST_TIMEZONE = pytz.timezone('US/Pacific')

def safe_emoji(emoji):
    try:
        emoji.encode('utf-8')
        return emoji
    except UnicodeEncodeError:
        emoji_map = {
            '⚔ ': '[SWORD] ',
            '🎣 ': '[FISH] ',
            '⚡ ': '[BOLT] ',
            '⚡': '[BOLT]',
            '⚔': '[SWORD]',
            '🎣': '[FISH]'
        }
        return emoji_map.get(emoji, '[?] ')

def get_next_event_time(current_time_pst, start_date, interval_hours):
    start_time_pst = PST_TIMEZONE.localize(start_date)
    time_since_start = current_time_pst - start_time_pst
    
    if time_since_start.total_seconds() < 0:
        return start_time_pst
    
    interval_seconds = interval_hours * 3600
    intervals_passed = int(time_since_start.total_seconds() // interval_seconds)
    next_event = start_time_pst + datetime.timedelta(seconds=(intervals_passed + 1) * interval_seconds)
    
    return next_event

def get_lifesteal_core_next(current_time_pst):
    return get_next_event_time(current_time_pst, LIFESTEAL_CORE_START, 6)

def get_dynamic_color(time_remaining):
    total_seconds = int(time_remaining.total_seconds())
    
    if total_seconds <= 0:
        return "&a"
    elif total_seconds <= 300:
        return "&a"
    elif total_seconds >= (6 * 3600 - 600):
        return "&6"
    elif total_seconds >= (3 * 3600 - 600):
        return "&6"
    else:
        return "&7"

def format_countdown_with_color(time_remaining, event_name, emoji=""):
    total_seconds = int(time_remaining.total_seconds())
    color = get_dynamic_color(time_remaining)
    safe_emoji_str = safe_emoji(emoji)

    if total_seconds <= 0:
        return f"{color}{safe_emoji_str}{event_name}: &l&aEVENT STARTING!"

    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60

    if hours > 0 and minutes > 0:
        return f"{color}{safe_emoji_str}{event_name}: {hours}h {minutes}m"
    elif hours > 0:
        return f"{color}{safe_emoji_str}{event_name}: {hours}h"
    elif minutes > 0:
        return f"{color}{safe_emoji_str}{event_name}: {minutes}m"
    else:
        return f"{color}{safe_emoji_str}{event_name}: &l<1m"

def debug_core_calculation():
    """Debug the Core event calculation with detailed logging."""
    try:
        pst = pytz.timezone('US/Pacific')
        now_utc = datetime.datetime.now(pytz.UTC)
        now_pst = now_utc.astimezone(pst)
        
        print(f"[DEBUG] Current time: {now_pst}")
        print(f"[DEBUG] Core start time: {PST_TIMEZONE.localize(LIFESTEAL_CORE_START)}")
        
        # Test Core calculation step by step
        core_next = get_lifesteal_core_next(now_pst)
        print(f"[DEBUG] Core next: {core_next}")
        
        core_remaining = core_next - now_pst
        print(f"[DEBUG] Core remaining: {core_remaining} ({core_remaining.total_seconds():.1f}s)")
        
        formatted = format_countdown_with_color(core_remaining, "Core", "⚡ ")
        print(f"[DEBUG] Core formatted: {repr(formatted)}")
        
        # Write to debug file
        debug_content = f"""==Section:TopLeft==
&c&l⚡ DEBUG
{formatted}
Time: {now_pst.strftime('%H:%M:%S')}
"""
        
        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            f.write(debug_content)
            f.flush()
        
        print(f"[DEBUG] Written to file: {OUTPUT_FILE}")
        return True
        
    except Exception as e:
        print(f"[ERROR] Core calculation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("Starting Core event debug monitoring...")
    print(f"Output file: {OUTPUT_FILE}")
    print(f"Update interval: {UPDATE_INTERVAL} seconds")
    
    success_count = 0
    failure_count = 0
    
    try:
        while True:
            timestamp = time.strftime('%H:%M:%S')
            print(f"\n[{timestamp}] Running debug calculation...")
            
            if debug_core_calculation():
                success_count += 1
                print(f"[SUCCESS] Total: {success_count} success, {failure_count} failures")
            else:
                failure_count += 1
                print(f"[FAILURE] Total: {success_count} success, {failure_count} failures")
            
            time.sleep(UPDATE_INTERVAL)
            
    except KeyboardInterrupt:
        print(f"\nDebug stopped. Final stats: {success_count} success, {failure_count} failures")

if __name__ == "__main__":
    main()
