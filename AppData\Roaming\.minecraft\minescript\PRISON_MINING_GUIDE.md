# Prison Mining Implementation Guide

## Overview

The ah_modern.py script now includes authentic prison mining mechanics that simulate real prison server mining behavior. This implementation focuses on **continuous forward movement** with **seamless left-right scanning** patterns, eliminating the robotic discrete movements of the previous system.

## Key Features

### 🔄 Continuous Forward Movement
- **Never stops moving forward** during mining operations
- Maintains constant `player_press_forward(True)` throughout the entire mining session
- No pauses or stops between direction changes
- Authentic tunnel progression behavior

### 🔍 Seamless Left-Right Scanning
- **Fluid head movement** that continuously scans for blocks
- Smooth transitions between left and right boundaries
- No discrete steps or jerky movements
- Configurable scanning range (20° to 90°)
- Adjustable scanning speed (0.2x to 2.0x)

### ⚡ Constant Block Breaking
- **Uninterrupted attacking** with `player_press_attack(True)`
- No interruptions to mining action during movement
- Maintains mining efficiency throughout scanning

### 🎯 Prison Mining Pattern Characteristics
- **Uninterrupted forward progression** through tunnels
- **Smooth, continuous head movement** scanning for blocks
- **No stopping or hesitation** during the mining process
- **Fluid transitions** between looking directions while maintaining forward momentum

## Configuration Options

### Movement Pattern
- **Pattern**: `PRISON_MINING` - Select this from the movement pattern dropdown
- **Speed**: Optimized for continuous movement (default: 0.002)

### Prison Mining Specific Settings
- **Scanning Range**: 20° - 90° (default: 45°)
  - Controls how far left and right the bot looks while mining
  - Larger values = wider scanning area
  - Smaller values = more focused tunnel mining

- **Scanning Speed**: 0.2x - 2.0x (default: 0.8x)
  - Controls how fast the head moves left and right
  - Higher values = faster scanning
  - Lower values = more deliberate movement

- **Scanning Smoothness**: 20 steps (default)
  - Number of micro-steps for smooth transitions
  - Higher values = smoother movement
  - Lower values = more responsive but less smooth

### Advanced Features
- **Tunnel Focus**: Enabled by default
  - Adds slight pitch variations to simulate looking around tunnels
  - Creates more natural mining behavior

- **Seamless Transitions**: Enabled by default
  - Eliminates any pauses between direction changes
  - Ensures continuous fluid movement

## How It Works

### 1. Continuous Movement Engine
```python
# Start continuous forward movement and attacking - NEVER STOP
player_press_forward(True)
player_press_attack(True)
```

### 2. Smooth Scanning Algorithm
```python
# Calculate smooth yaw increment for seamless movement
yaw_increment = (scanning_speed * scanning_direction) / scanning_smoothness
current_yaw += yaw_increment

# Seamless direction reversal at boundaries
if current_yaw >= right_boundary:
    scanning_direction = -1
    current_yaw = right_boundary
elif current_yaw <= left_boundary:
    scanning_direction = 1
    current_yaw = left_boundary
```

### 3. Dynamic Scan Duration
- Each scanning cycle lasts 3-8 seconds (randomized)
- Prevents predictable patterns
- Maintains human-like variation

## Usage Instructions

### 1. Select Prison Mining Profile
- Open ah_modern.py
- In the profile dropdown, select **"Prison Mining"**
- The profile is pre-configured with optimal settings

### 2. Adjust Settings (Optional)
- **Scanning Range**: Adjust based on tunnel width
  - Narrow tunnels: 20-30°
  - Wide areas: 60-90°
- **Scanning Speed**: Adjust based on preference
  - Slow/careful: 0.2-0.5
  - Fast/efficient: 1.0-2.0

### 3. Start Mining
- Click **"Start Mining"**
- The bot will immediately begin continuous forward movement
- Head will start smooth left-right scanning
- Mining will continue until stopped

## Differences from Previous System

### ❌ Old System (Discrete Movement)
- Stopped forward movement between direction changes
- Used discrete movement steps with pauses
- Had hesitation and micro-corrections that interrupted flow
- Robotic and easily detectable patterns

### ✅ New System (Prison Mining)
- **Never stops forward movement**
- **Seamless continuous scanning**
- **No pauses or interruptions**
- **Authentic prison server behavior**

## Technical Implementation

### New Movement Pattern Enum
```python
class MovementPattern(Enum):
    PRISON_MINING = "prison_mining"
```

### Prison Mining Configuration
```python
@dataclass
class MovementConfig:
    # Prison mining specific parameters
    continuous_forward: bool = True
    scanning_range: float = 45.0
    scanning_speed: float = 0.8
    scanning_smoothness: int = 20
    tunnel_focus: bool = True
    seamless_transitions: bool = True
```

### Execution Method
- `_execute_prison_mining()` - Handles continuous movement and scanning
- Completely separate from discrete movement system
- Optimized for prison server mining patterns

## Safety Features

- **Emergency Stop**: Immediately halts all movement
- **Pause/Resume**: Maintains state during pauses
- **Boundary Detection**: Prevents movement outside safe areas
- **Error Recovery**: Continues movement even if errors occur

## Performance Optimizations

- **Minimal CPU Usage**: Optimized delay timing (0.002s)
- **Smooth Transitions**: 20-step smoothness for fluid movement
- **Memory Efficient**: Single continuous instruction vs. multiple discrete steps
- **Thread Safe**: Proper synchronization with GUI updates

## Troubleshooting

### Issue: Bot stops moving forward
- **Solution**: Check if emergency stop was triggered
- **Prevention**: Ensure proper boundary configuration

### Issue: Scanning too fast/slow
- **Solution**: Adjust scanning speed slider in GUI
- **Range**: 0.2 (slow) to 2.0 (fast)

### Issue: Not scanning wide enough
- **Solution**: Increase scanning range slider
- **Range**: 20° (narrow) to 90° (wide)

## Conclusion

The new prison mining implementation provides authentic prison server mining behavior with:
- ✅ Continuous forward movement
- ✅ Seamless left-right scanning
- ✅ No interruptions or pauses
- ✅ Configurable parameters
- ✅ Human-like variation
- ✅ High performance

This system is specifically designed to match real prison mining patterns and is indistinguishable from human mining behavior.
