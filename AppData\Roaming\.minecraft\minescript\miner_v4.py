#!/usr/bin/env python3
"""
Advanced Minecraft Mining Bot with Modern GUI
Professional mining automation with comprehensive safety systems, statistics tracking,
and a modern, user-friendly interface.

Features:
- Multiple mining profiles with different patterns
- Advanced safety systems and emergency stops
- Comprehensive statistics and session tracking
- Modern GUI with persistent settings
- Real-time monitoring and debug output
- Profile memory and user preferences

Author: Mining Bot Team
Version: 4.0 - Complete GUI Overhaul
Last Updated: 2024
"""

import customtkinter as ctk
from queue import Queue
import time
import threading
import sys
import random
import json
import os
from datetime import datetime
from tkinter import messagebox
import tkinter as tk

# Modern color scheme
COLORS = {
    "primary": "#2B2B2B",
    "secondary": "#3B3B3B", 
    "accent": "#0078D4",
    "success": "#107C10",
    "warning": "#FF8C00",
    "error": "#D13438",
    "text_primary": "#FFFFFF",
    "text_secondary": "#CCCCCC",
    "text_muted": "#999999",
    "card_bg": "#404040",
    "hover": "#4A4A4A"
}

# Set modern appearance
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

# Configuration file paths
CONFIG_DIR = os.path.join(os.path.expanduser("~"), "AppData", "Roaming", ".minecraft", "minescript")
SETTINGS_FILE = os.path.join(CONFIG_DIR, "miner_settings.json")
STATS_FILE = os.path.join(CONFIG_DIR, "miner_stats.json")
SESSION_HISTORY_FILE = os.path.join(CONFIG_DIR, "session_history.json")

# Ensure config directory exists
os.makedirs(CONFIG_DIR, exist_ok=True)

# Global state for mining operations
STATE = {
    "running": False,
    "paused": False,
    "start_time": None,
    "current_direction": None,
    "emergency_stop": False,
    "mining_thread": None,
    "gui_thread": None,
    "starting_position": None,
    "last_position": None,
    "last_movement_time": None,
    "movement_check_count": 0,
    "beeping_active": False,
    "beeping_thread": None,
    "pause_state_locked": False,
    "debug_queue": Queue(),
    "stats_queue": Queue(),  # Queue for statistics updates

    # Random movement variation for Line Sacred
    "last_random_movement": 0,
    "random_movement_active": False,
    "random_movement_end_time": 0,
    "wiggle_pattern_active": False,
    "wiggle_step": 0,
    "wiggle_step_start_time": 0,
    
    # Yaw wiggling for Line Sacred
    "last_yaw_wiggle": 0,
    "yaw_wiggle_active": False,
    "yaw_wiggle_end_time": 0,
    "yaw_wiggle_step": 0,
    "yaw_wiggle_step_start_time": 0,
    "yaw_wiggle_next_change": 0,
    
    # Enhanced statistics
    "session_stats": {
        "blocks_mined": 0,
        "direction_changes": 0,
        "distance_traveled": 0,
        "emergency_stops": 0,
        "pause_count": 0
    }
}

# Try to import audio module
try:
    import winsound
    AUDIO_AVAILABLE = "winsound"
except ImportError:
    try:
        import playsound
        AUDIO_AVAILABLE = "playsound"
    except ImportError:
        AUDIO_AVAILABLE = None

# Import Minescript functions
try:
    import minescript
    from minescript import (
        player_press_forward,
        player_press_backward,
        player_press_left,
        player_press_right,
        player_press_attack,
        player_position,
        player_set_orientation,
        player_orientation,
        echo,
        execute
    )
    MINESCRIPT_AVAILABLE = True
except ImportError:
    MINESCRIPT_AVAILABLE = False
    echo = print
    def player_press_forward(state): pass
    def player_press_backward(state): pass
    def player_press_left(state): pass
    def player_press_right(state): pass
    def player_press_attack(state): pass
    def player_position(): return [0, 0, 0]
    def player_set_orientation(yaw, pitch): pass
    def player_orientation(): return [0, 0]
    def execute(command): pass

# Profile-based configuration system
PROFILES = {
    "circle_sacred": {
        "name": "Circle Sacred",
        "mining_area": {
            "min_x": -39,
            "min_z": -39,
            "max_x": 40,
            "max_z": 40
        },
        "mining_type": "square",
        "movement_speed": 0.001,
        "position_check_interval": 0.02,
        "safety_margin": 5,
        "home_command": "/home gen"
    },
    "line_sacred": {
        "name": "Line Sacred",
        "mining_line": {
            "x": 0,
            "y": 178,
            "min_z": -43,
            "max_z": 43
        },
        "mining_type": "linear",
        "movement_speed": 0.001,
        "position_check_interval": 0.02,
        "tolerance": 3.0,
        "home_command": "/home gen"
    }
}

# Current profile
CURRENT_PROFILE = "line_sacred"
CONFIG = PROFILES[CURRENT_PROFILE]

def get_config():
    """Get current profile configuration."""
    return PROFILES[CURRENT_PROFILE]

# Settings management
def load_settings():
    """Load user settings from file."""
    default_settings = {
        "last_profile": "line_sacred",
        "window_geometry": "500x800+100+100",
        "theme": "dark",
        "debug_level": "normal",
        "auto_save_stats": True,
        "show_tooltips": True,
        "confirm_actions": True
    }
    
    try:
        if os.path.exists(SETTINGS_FILE):
            with open(SETTINGS_FILE, 'r') as f:
                settings = json.load(f)
                # Merge with defaults to handle new settings
                for key, value in default_settings.items():
                    if key not in settings:
                        settings[key] = value
                return settings
    except Exception as e:
        print(f"Error loading settings: {e}")
    
    return default_settings

def save_settings(settings):
    """Save user settings to file."""
    try:
        with open(SETTINGS_FILE, 'w') as f:
            json.dump(settings, f, indent=2)
    except Exception as e:
        print(f"Error saving settings: {e}")

# Statistics management
def load_stats():
    """Load statistics from file."""
    default_stats = {
        "total_runtime_seconds": 0,
        "total_sessions": 0,
        "total_blocks_mined": 0,
        "total_distance_traveled": 0,
        "total_direction_changes": 0,
        "total_emergency_stops": 0,
        "profiles_used": {},
        "last_updated": datetime.now().isoformat()
    }
    
    try:
        if os.path.exists(STATS_FILE):
            with open(STATS_FILE, 'r') as f:
                return json.load(f)
    except Exception as e:
        print(f"Error loading stats: {e}")
    
    return default_stats

def save_stats(stats):
    """Save statistics to file."""
    try:
        stats["last_updated"] = datetime.now().isoformat()
        with open(STATS_FILE, 'w') as f:
            json.dump(stats, f, indent=2)
    except Exception as e:
        print(f"Error saving stats: {e}")

# Session history management
def save_session_history(session_data):
    """Save session to history file."""
    try:
        history = []
        if os.path.exists(SESSION_HISTORY_FILE):
            with open(SESSION_HISTORY_FILE, 'r') as f:
                history = json.load(f)
        
        history.append(session_data)
        
        # Keep only last 100 sessions
        if len(history) > 100:
            history = history[-100:]
        
        with open(SESSION_HISTORY_FILE, 'w') as f:
            json.dump(history, f, indent=2)
    except Exception as e:
        print(f"Error saving session history: {e}")

def debug_log(message):
    """Thread-safe debug logging."""
    timestamp = datetime.now().strftime("%H:%M:%S")
    formatted_message = f"[{timestamp}] {message}"
    STATE["debug_queue"].put(formatted_message)

def format_time_from_seconds(seconds):
    """Format seconds into HH:MM:SS."""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

# Audio system
def start_beeping():
    """Start emergency beeping sound."""
    if STATE["beeping_active"]:
        return

    STATE["beeping_active"] = True

    def beep_loop():
        while STATE["beeping_active"]:
            try:
                if AUDIO_AVAILABLE == "winsound":
                    winsound.Beep(1000, 500)
                elif AUDIO_AVAILABLE == "playsound":
                    # Would need a beep sound file
                    pass
                time.sleep(0.5)
            except Exception as e:
                debug_log(f"§c[AUDIO] Beeping error: {e}")
                break

    STATE["beeping_thread"] = threading.Thread(target=beep_loop, daemon=True)
    STATE["beeping_thread"].start()

def stop_beeping():
    """Stop emergency beeping sound."""
    STATE["beeping_active"] = False
    if STATE["beeping_thread"]:
        STATE["beeping_thread"] = None

# Movement system
def stop_all_movements():
    """Stop all player movements and attacks."""
    try:
        player_press_forward(False)
        player_press_backward(False)
        player_press_left(False)
        player_press_right(False)
        player_press_attack(False)
        debug_log("§c[Miner] All movements stopped")
    except Exception as e:
        print(f"Error stopping movements: {e}")

def apply_movement_direction(direction: str, force_apply: bool = False):
    """Apply movement keys for the specified direction with optimization."""
    if not direction:
        return

    # Direction key mappings
    direction_keys = {
        "FORWARD": {"forward": True, "backward": False, "left": False, "right": False},
        "BACKWARD": {"forward": False, "backward": True, "left": False, "right": False},
        "FORWARD_RIGHT": {"forward": True, "backward": False, "left": False, "right": True},
        "FORWARD_LEFT": {"forward": True, "backward": False, "left": True, "right": False},
        "BACKWARD_RIGHT": {"forward": False, "backward": True, "left": False, "right": True},
        "BACKWARD_LEFT": {"forward": False, "backward": True, "left": True, "right": False}
    }

    current_dir = STATE.get("last_applied_direction", None)

    # Get current and new key states
    current_keys = direction_keys.get(current_dir, {"forward": False, "backward": False, "left": False, "right": False})
    new_keys = direction_keys.get(direction, {"forward": False, "backward": False, "left": False, "right": False})

    # Apply movement keys based on direction with proper sequencing
    config = get_config()
    if config["mining_type"] == "linear":
        # For linear movement, ensure forward/backward conflict resolution
        if current_keys["forward"] and new_keys["backward"]:
            # Switching from forward to backward - release forward first, then immediately apply backward
            player_press_forward(False)
            player_press_backward(True)
            debug_log("§d[Miner] Instant forward→backward transition")
            return  # Skip normal key application since we handled it
        elif current_keys["backward"] and new_keys["forward"]:
            # Switching from backward to forward - release backward first, then immediately apply forward
            player_press_backward(False)
            player_press_forward(True)
            debug_log("§d[Miner] Instant backward→forward transition")
            return  # Skip normal key application since we handled it

    # Apply movement keys
    if force_apply or current_keys["forward"] != new_keys["forward"]:
        player_press_forward(new_keys["forward"])
    if force_apply or current_keys["backward"] != new_keys["backward"]:
        player_press_backward(new_keys["backward"])
    if force_apply or current_keys["left"] != new_keys["left"]:
        player_press_left(new_keys["left"])
    if force_apply or current_keys["right"] != new_keys["right"]:
        player_press_right(new_keys["right"])

    # Always keep attacking while mining - force attack every time
    try:
        player_press_attack(True)
    except Exception as e:
        debug_log(f"§c[Miner] Attack error in apply_movement_direction: {e}")

    STATE["last_applied_direction"] = direction

# Yaw wiggling system for Line Sacred
def apply_yaw_wiggle():
    """Apply yaw wiggling for Line Sacred profile only."""
    config = get_config()
    if config["mining_type"] != "linear":
        return

    current_time = time.time()

    # Check if we should start a new yaw wiggle session (5% chance every 2 minutes)
    if not STATE["yaw_wiggle_active"] and current_time - STATE["last_yaw_wiggle"] >= 120:
        if random.random() < 0.05:  # 5% chance
            STATE["yaw_wiggle_active"] = True
            STATE["yaw_wiggle_end_time"] = current_time + random.uniform(8*60, 12*60)  # 8-12 minutes
            STATE["yaw_wiggle_step"] = 0
            STATE["yaw_wiggle_step_start_time"] = current_time
            STATE["yaw_wiggle_next_change"] = current_time + random.uniform(60, 120)  # 1-2 minutes
            debug_log("§6[Miner] Yaw wiggle session started")
        STATE["last_yaw_wiggle"] = current_time

    # Handle active yaw wiggle session
    if STATE["yaw_wiggle_active"]:
        if current_time >= STATE["yaw_wiggle_end_time"]:
            # End yaw wiggle session
            STATE["yaw_wiggle_active"] = False
            debug_log("§6[Miner] Yaw wiggle session ended")
        elif current_time >= STATE["yaw_wiggle_next_change"]:
            # Change yaw position
            try:
                current_orientation = player_orientation()
                if current_orientation and len(current_orientation) >= 2:
                    current_yaw = current_orientation[0]

                    if STATE["yaw_wiggle_step"] == 0:
                        # Move to -1 degree
                        new_yaw = current_yaw - 1
                        player_set_orientation(new_yaw, current_orientation[1])
                        STATE["yaw_wiggle_step"] = 1
                        debug_log("§6[Miner] Yaw wiggle: -1°")
                    else:
                        # Move to +1 degree
                        new_yaw = current_yaw + 1
                        player_set_orientation(new_yaw, current_orientation[1])
                        STATE["yaw_wiggle_step"] = 0
                        debug_log("§6[Miner] Yaw wiggle: +1°")

                    # Set next change time
                    STATE["yaw_wiggle_next_change"] = current_time + random.uniform(60, 120)  # 1-2 minutes
            except Exception as e:
                debug_log(f"§c[Miner] Yaw wiggle error: {e}")

# Random movement system for Line Sacred
def apply_random_movement():
    """Apply random left/right wiggling for Line Sacred profile only."""
    config = get_config()
    if config["mining_type"] != "linear":
        return

    current_time = time.time()

    # Check if we should start a new wiggle pattern (5% chance every 2 minutes)
    if not STATE["wiggle_pattern_active"] and current_time - STATE["last_random_movement"] >= 120:
        if random.random() < 0.05:  # 5% chance
            STATE["wiggle_pattern_active"] = True
            STATE["wiggle_step"] = 0
            STATE["wiggle_step_start_time"] = current_time
            STATE["random_movement_end_time"] = current_time + random.uniform(4, 8)  # 4-8 seconds total
            debug_log("§5[Miner] Random wiggle pattern started")
        STATE["last_random_movement"] = current_time

    # Handle active wiggle pattern
    if STATE["wiggle_pattern_active"]:
        if current_time >= STATE["random_movement_end_time"]:
            # End wiggle pattern
            STATE["wiggle_pattern_active"] = False
            player_press_left(False)
            player_press_right(False)
            debug_log("§5[Miner] Random wiggle pattern ended")
        else:
            # Execute wiggle steps
            step_duration = random.uniform(0.5, 1.0)  # 0.5-1.0 seconds per step

            if current_time - STATE["wiggle_step_start_time"] >= step_duration:
                if STATE["wiggle_step"] == 0:
                    # Left wiggle
                    player_press_right(False)
                    player_press_left(True)
                    STATE["wiggle_step"] = 1
                    debug_log("§5[Miner] Wiggle: LEFT")
                elif STATE["wiggle_step"] == 1:
                    # Wait step
                    player_press_left(False)
                    STATE["wiggle_step"] = 2
                    debug_log("§5[Miner] Wiggle: WAIT")
                elif STATE["wiggle_step"] == 2:
                    # Right wiggle
                    player_press_left(False)
                    player_press_right(True)
                    STATE["wiggle_step"] = 3
                    debug_log("§5[Miner] Wiggle: RIGHT")
                elif STATE["wiggle_step"] == 3:
                    # Wait step
                    player_press_right(False)
                    STATE["wiggle_step"] = 0
                    debug_log("§5[Miner] Wiggle: WAIT")

                STATE["wiggle_step_start_time"] = current_time

# Direction detection systems
def get_mining_direction(x, z):
    """Determine mining direction based on current position and profile."""
    config = get_config()

    if config["mining_type"] == "square":
        return get_square_direction(x, z)
    elif config["mining_type"] == "linear":
        return get_linear_direction(x, z)

    return None

def get_square_direction(x, z):
    """Get direction for square pattern mining."""
    config = get_config()
    area = config["mining_area"]

    # Define corners with tolerance
    tolerance = 1.5

    # Corner detection
    top_left = (abs(x - area["min_x"]) <= tolerance and abs(z - area["min_z"]) <= tolerance)
    top_right = (abs(x - area["max_x"]) <= tolerance and abs(z - area["min_z"]) <= tolerance)
    bottom_right = (abs(x - area["max_x"]) <= tolerance and abs(z - area["max_z"]) <= tolerance)
    bottom_left = (abs(x - area["min_x"]) <= tolerance and abs(z - area["max_z"]) <= tolerance)

    # Edge detection
    on_top_edge = abs(z - area["min_z"]) <= tolerance
    on_right_edge = abs(x - area["max_x"]) <= tolerance
    on_bottom_edge = abs(z - area["max_z"]) <= tolerance
    on_left_edge = abs(x - area["min_x"]) <= tolerance

    # Corner priorities (clockwise movement)
    if top_left:
        return "FORWARD_RIGHT"  # From top-left, go forward+right
    elif top_right:
        return "BACKWARD_RIGHT"  # From top-right, go backward+right
    elif bottom_right:
        return "BACKWARD_LEFT"  # From bottom-right, go backward+left
    elif bottom_left:
        return "FORWARD_LEFT"  # From bottom-left, go forward+left

    # Edge directions (clockwise)
    elif on_top_edge:
        return "FORWARD_RIGHT"  # Top edge: forward+right
    elif on_right_edge:
        return "BACKWARD_RIGHT"  # Right edge: backward+right
    elif on_bottom_edge:
        return "BACKWARD_LEFT"  # Bottom edge: backward+left
    elif on_left_edge:
        return "FORWARD_LEFT"  # Left edge: forward+left

    # Default fallback
    return "FORWARD_RIGHT"

def get_linear_direction(x, z):
    """Get direction for linear pattern mining."""
    config = get_config()
    line = config["mining_line"]

    # Check if at the endpoints
    if z <= line["min_z"] + 1:
        return "FORWARD"  # At min Z, go forward (positive Z)
    elif z >= line["max_z"] - 1:
        return "BACKWARD"  # At max Z, go backward (negative Z)

    # Continue in current direction if in middle
    current_dir = STATE.get("current_direction")
    if current_dir in ["FORWARD", "BACKWARD"]:
        return current_dir

    # Default to forward if no current direction
    return "FORWARD"

# Safety systems
def check_boundary_failsafe(x, y, z):
    """Check if position is within safe boundaries for square mining."""
    config = get_config()
    area = config["mining_area"]
    safety_margin = config.get("safety_margin", 5)

    # Check X and Z boundaries with safety margin
    x_safe = (area["min_x"] - safety_margin) <= x <= (area["max_x"] + safety_margin)
    z_safe = (area["min_z"] - safety_margin) <= z <= (area["max_z"] + safety_margin)

    # Check Y level (around 178 with tolerance)
    y_safe = 175 <= y <= 181

    return x_safe and z_safe and y_safe

def check_line_sacred_boundary(x, y, z):
    """Check if position is within safe boundaries for linear mining."""
    config = get_config()
    line = config["mining_line"]
    tolerance = config["tolerance"]

    # Check X coordinate (must stay on line)
    x_safe = abs(x - line["x"]) <= tolerance

    # Check Y coordinate (must stay at correct level)
    y_safe = abs(y - line["y"]) <= tolerance

    # Check Z coordinate (must stay within line bounds)
    z_safe = (line["min_z"] - tolerance) <= z <= (line["max_z"] + tolerance)

    return x_safe and y_safe and z_safe

# Main mining loop
def mining_loop():
    """Main mining loop with enhanced error handling and statistics."""
    try:
        debug_log("§a[Miner] Mining loop started")

        # Get starting position
        start_pos = player_position()
        if not start_pos or len(start_pos) < 3:
            echo("§c[Miner] Could not get starting position!")
            return

        STATE["starting_position"] = start_pos
        debug_log(f"§a[Miner] Starting position: {start_pos}")

        # Initialize direction
        config = get_config()
        initial_direction = get_mining_direction(start_pos[0], start_pos[2])
        if not initial_direction:
            echo("§c[Miner] Could not determine initial direction!")
            return

        # Start mining immediately
        try:
            player_press_attack(True)
        except Exception as e:
            echo(f"§c[Miner] Error starting attack: {e}")
            return

        # Set current direction and apply movement
        STATE["current_direction"] = None
        apply_movement_direction(initial_direction)
        STATE["current_direction"] = initial_direction

        # Initialize movement detection
        STATE["last_position"] = start_pos
        STATE["last_movement_time"] = time.time()
        STATE["movement_check_count"] = 0

        # Initialize timing variables
        last_position_check = time.time()
        last_boundary_check = time.time()
        last_movement_refresh = time.time()

        while STATE["running"] and not STATE["emergency_stop"]:
            current_time = time.time()

            # Check if paused
            if STATE["paused"]:
                if not STATE["pause_state_locked"]:
                    stop_all_movements()
                    STATE["pause_state_locked"] = True
                time.sleep(0.02)
                continue
            else:
                if STATE["pause_state_locked"]:
                    # Resume mining
                    if STATE["current_direction"]:
                        apply_movement_direction(STATE["current_direction"], force_apply=True)
                    STATE["pause_state_locked"] = False

            # Position-based direction detection
            if current_time - last_position_check >= config["position_check_interval"]:
                try:
                    pos = player_position()
                    if pos and len(pos) >= 3:
                        x, y, z = pos[0], pos[1], pos[2]

                        # Get direction based on profile
                        detected_direction = get_mining_direction(x, z)
                        position_str = f"({x:.1f}, {z:.1f})"

                        # Apply direction change immediately if different and valid
                        if detected_direction and detected_direction != STATE["current_direction"]:
                            debug_log(f"§e[Miner] Position {position_str} - Direction change: {STATE['current_direction']} → {detected_direction}")
                            STATE["current_direction"] = detected_direction
                            STATE["session_stats"]["direction_changes"] += 1
                            echo(f"§a[Miner] Direction change: {detected_direction}")
                            apply_movement_direction(detected_direction, force_apply=True)

                            # Ensure attack continues during direction changes
                            try:
                                player_press_attack(True)
                            except Exception as e:
                                debug_log(f"§c[Miner] Attack error during direction change: {e}")

                except Exception as e:
                    echo(f"§c[Miner] Error in direction change logic: {e}")
                    debug_log(f"§c[Miner] Direction change error details: {str(e)}")

                last_position_check = current_time

            # Enhanced boundary failsafe check
            if current_time - last_boundary_check >= 0.5:
                try:
                    pos = player_position()
                    if pos and len(pos) >= 3:
                        # Use appropriate boundary check based on profile
                        boundary_safe = False
                        if config["mining_type"] == "linear":
                            boundary_safe = check_line_sacred_boundary(pos[0], pos[1], pos[2])
                        else:
                            boundary_safe = check_boundary_failsafe(pos[0], pos[1], pos[2])

                        if not boundary_safe:
                            echo("§c[FAILSAFE] Boundary violation - Emergency stop!")
                            STATE["emergency_stop"] = True
                            STATE["session_stats"]["emergency_stops"] += 1
                            start_beeping()
                            break
                except Exception as e:
                    echo(f"§c[FAILSAFE] Error checking boundary: {e}")
                    debug_log(f"§c[FAILSAFE] Boundary check error: {str(e)}")
                last_boundary_check = current_time

            # Movement detection safeguard
            if current_time - STATE["last_movement_time"] >= 1.0:
                try:
                    pos = player_position()
                    if pos and STATE["last_position"] is not None:
                        # Calculate distance moved
                        last_pos = STATE["last_position"]
                        distance_moved = max(
                            abs(pos[0] - last_pos[0]),
                            abs(pos[1] - last_pos[1]),
                            abs(pos[2] - last_pos[2])
                        )

                        STATE["session_stats"]["distance_traveled"] += distance_moved

                        if distance_moved < 0.5:
                            STATE["movement_check_count"] += 1
                            if STATE["movement_check_count"] >= 3:
                                echo("§c[SAFEGUARD] Player stuck - Emergency stop activated!")
                                STATE["emergency_stop"] = True
                                STATE["session_stats"]["emergency_stops"] += 1
                                start_beeping()
                                break
                        else:
                            STATE["movement_check_count"] = 0

                        STATE["last_position"] = pos
                        STATE["last_movement_time"] = current_time
                except Exception as e:
                    debug_log(f"§c[Miner] Movement detection error: {e}")

            # Periodic movement refresh
            if current_time - last_movement_refresh >= 2.0:
                try:
                    if STATE["current_direction"]:
                        apply_movement_direction(STATE["current_direction"], force_apply=True)
                        debug_log(f"§7[Miner] Movement refresh: {STATE['current_direction']}")
                        # Ensure attack is maintained during movement refresh
                        player_press_attack(True)
                except Exception as e:
                    debug_log(f"§c[Miner] Movement refresh error: {e}")
                last_movement_refresh = current_time

            # Ensure continuous attacking - NEVER STOP ATTACKING
            try:
                player_press_attack(True)
                # For Line Sacred profile, enforce attack even more aggressively
                if config["mining_type"] == "linear":
                    player_press_attack(True)  # Double enforcement
                    STATE["session_stats"]["blocks_mined"] += 0.1  # Estimate
            except Exception as e:
                debug_log(f"§c[Miner] Attack error: {e}")
                try:
                    player_press_attack(True)  # Retry
                except:
                    pass

            # Apply profile-specific behaviors
            try:
                apply_yaw_wiggle()  # For Line Sacred profile only
                apply_random_movement()  # For Line Sacred profile only
            except Exception as e:
                debug_log(f"§c[Miner] Behavior error: {e}")

            # Optimized delay to prevent excessive CPU usage
            time.sleep(config["movement_speed"])

    except Exception as e:
        echo(f"§c[Miner] Error in mining loop: {e}")
        debug_log(f"§c[Miner] Mining loop error: {str(e)}")
    finally:
        stop_all_movements()
        echo("§c[Miner] Mining stopped")
        debug_log("§c[Miner] Mining loop ended")

class ModernMinerGUI(ctk.CTk):
    """Modern, professional mining bot GUI with enhanced features."""

    def __init__(self):
        super().__init__()

        # Load settings
        self.settings = load_settings()
        self.stats = load_stats()

        # Set current profile from settings
        global CURRENT_PROFILE, CONFIG
        CURRENT_PROFILE = self.settings["last_profile"]
        CONFIG = get_config()

        # Configure window
        self.title("🔨 Advanced Mining Bot v4.0")
        self.configure_window()

        # Initialize GUI components
        self.setup_styles()
        self.setup_ui()
        self.setup_tooltips()
        self.setup_keyboard_shortcuts()

        # Start update loops
        self.update_stats_loop()
        self.update_debug_panel()

        # Handle window close
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def configure_window(self):
        """Configure window properties with persistent settings."""
        # Apply saved geometry or default
        geometry = self.settings.get("window_geometry", "500x800+100+100")
        self.geometry(geometry)

        # Set minimum size
        self.minsize(450, 600)
        self.resizable(True, True)

        # Configure grid weights for responsive design
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(1, weight=1)

    def setup_styles(self):
        """Setup modern styling and color scheme."""
        # Configure appearance based on settings
        appearance_mode = self.settings.get("theme", "dark")
        ctk.set_appearance_mode(appearance_mode)

        # Store color references for easy access
        self.colors = COLORS

    def setup_ui(self):
        """Set up the modern user interface."""
        # Header with status indicator
        self.setup_header()

        # Main content with tabs
        self.setup_main_content()

        # Status bar
        self.setup_status_bar()

    def setup_header(self):
        """Setup modern header with status indicator."""
        header_frame = ctk.CTkFrame(self, fg_color=self.colors["primary"])
        header_frame.grid(row=0, column=0, sticky="ew", padx=0, pady=0)
        header_frame.grid_columnconfigure(1, weight=1)

        # Status indicator
        self.status_indicator = ctk.CTkLabel(
            header_frame,
            text="●",
            font=ctk.CTkFont(size=20),
            text_color=self.colors["error"],
            width=30
        )
        self.status_indicator.grid(row=0, column=0, padx=(20, 10), pady=15)

        # Title with profile info
        title_text = f"🔨 Advanced Mining Bot v4.0"
        self.title_label = ctk.CTkLabel(
            header_frame,
            text=title_text,
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=self.colors["text_primary"]
        )
        self.title_label.grid(row=0, column=1, padx=10, pady=15, sticky="w")

        # Current profile indicator
        profile_name = CONFIG["name"]
        self.profile_indicator = ctk.CTkLabel(
            header_frame,
            text=f"Profile: {profile_name}",
            font=ctk.CTkFont(size=12),
            text_color=self.colors["text_secondary"]
        )
        self.profile_indicator.grid(row=0, column=2, padx=(10, 20), pady=15, sticky="e")

    def setup_main_content(self):
        """Setup main content area with modern tabview."""
        # Create modern tabview
        self.tabview = ctk.CTkTabview(
            self,
            fg_color=self.colors["secondary"],
            segmented_button_fg_color=self.colors["card_bg"],
            segmented_button_selected_color=self.colors["accent"],
            segmented_button_selected_hover_color=self.colors["accent"]
        )
        self.tabview.grid(row=1, column=0, padx=20, pady=(10, 0), sticky="nsew")

        # Add tabs with icons
        self.tabview.add("⚡ Mining Control")
        self.tabview.add("📊 Statistics")
        self.tabview.add("⚙️ Settings")
        self.tabview.add("📋 Debug")

        # Setup tab content
        self.setup_mining_control_tab()
        self.setup_statistics_tab()
        self.setup_settings_tab()
        self.setup_debug_tab()

    def setup_mining_control_tab(self):
        """Setup the main mining control interface."""
        tab = self.tabview.tab("⚡ Mining Control")
        tab.grid_columnconfigure(0, weight=1)
        tab.grid_rowconfigure(0, weight=1)

        # Scrollable content
        scroll_frame = ctk.CTkScrollableFrame(
            tab,
            fg_color="transparent"
        )
        scroll_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        scroll_frame.grid_columnconfigure(0, weight=1)

        # Profile selection card
        self.setup_profile_card(scroll_frame, 0)

        # Control buttons card
        self.setup_control_card(scroll_frame, 1)

        # Quick commands card
        self.setup_commands_card(scroll_frame, 2)

        # Real-time status card
        self.setup_status_card(scroll_frame, 3)

    def setup_profile_card(self, parent, row):
        """Setup profile selection card."""
        card = ctk.CTkFrame(parent, fg_color=self.colors["card_bg"])
        card.grid(row=row, column=0, sticky="ew", padx=5, pady=5)
        card.grid_columnconfigure(1, weight=1)

        # Card title
        title = ctk.CTkLabel(
            card,
            text="🎯 Mining Profile",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.colors["text_primary"]
        )
        title.grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10), sticky="w")

        # Profile selection
        ctk.CTkLabel(
            card,
            text="Active Profile:",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=self.colors["text_secondary"]
        ).grid(row=1, column=0, padx=(15, 10), pady=10, sticky="w")

        self.profile_var = ctk.StringVar(value=CURRENT_PROFILE)
        self.profile_dropdown = ctk.CTkOptionMenu(
            card,
            variable=self.profile_var,
            values=list(PROFILES.keys()),
            command=self.change_profile,
            font=ctk.CTkFont(size=12),
            fg_color=self.colors["accent"],
            button_color=self.colors["accent"],
            button_hover_color=self.colors["hover"]
        )
        self.profile_dropdown.grid(row=1, column=1, padx=(0, 15), pady=10, sticky="ew")

        # Profile description
        profile_desc = CONFIG.get("name", "Unknown Profile")
        self.profile_desc_label = ctk.CTkLabel(
            card,
            text=f"📝 {profile_desc}",
            font=ctk.CTkFont(size=11),
            text_color=self.colors["text_muted"]
        )
        self.profile_desc_label.grid(row=2, column=0, columnspan=2, padx=15, pady=(0, 15), sticky="w")

    def setup_control_card(self, parent, row):
        """Setup main control buttons card."""
        card = ctk.CTkFrame(parent, fg_color=self.colors["card_bg"])
        card.grid(row=row, column=0, sticky="ew", padx=5, pady=5)
        card.grid_columnconfigure((0, 1), weight=1)

        # Card title
        title = ctk.CTkLabel(
            card,
            text="🎮 Mining Controls",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.colors["text_primary"]
        )
        title.grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10), sticky="w")

        # Start/Stop button
        self.start_stop_btn = ctk.CTkButton(
            card,
            text="▶️ Start Mining",
            command=self.toggle_mining,
            font=ctk.CTkFont(size=16, weight="bold"),
            height=50,
            fg_color=self.colors["success"],
            hover_color="#0E6B0E"
        )
        self.start_stop_btn.grid(row=1, column=0, padx=(15, 5), pady=10, sticky="ew")

        # Pause/Resume button
        self.pause_resume_btn = ctk.CTkButton(
            card,
            text="⏸️ Pause",
            command=self.toggle_pause,
            font=ctk.CTkFont(size=16),
            height=50,
            fg_color=self.colors["warning"],
            hover_color="#E67E00",
            state="disabled"
        )
        self.pause_resume_btn.grid(row=1, column=1, padx=(5, 15), pady=10, sticky="ew")

        # Emergency stop button
        emergency_btn = ctk.CTkButton(
            card,
            text="🛑 EMERGENCY STOP",
            command=self.emergency_stop,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            fg_color=self.colors["error"],
            hover_color="#B12B2F"
        )
        emergency_btn.grid(row=2, column=0, columnspan=2, padx=15, pady=(5, 15), sticky="ew")

    def setup_commands_card(self, parent, row):
        """Setup quick commands card."""
        card = ctk.CTkFrame(parent, fg_color=self.colors["card_bg"])
        card.grid(row=row, column=0, sticky="ew", padx=5, pady=5)
        card.grid_columnconfigure((0, 1), weight=1)

        # Card title
        title = ctk.CTkLabel(
            card,
            text="⚡ Quick Commands",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.colors["text_primary"]
        )
        title.grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10), sticky="w")

        # Home Gen button
        home_gen_btn = ctk.CTkButton(
            card,
            text="🏠 Home Gen",
            command=self.execute_home_gen,
            font=ctk.CTkFont(size=14),
            height=35,
            fg_color=self.colors["accent"],
            hover_color="#005A9E"
        )
        home_gen_btn.grid(row=1, column=0, padx=(15, 5), pady=5, sticky="ew")

        # Skyblock button
        skyblock_btn = ctk.CTkButton(
            card,
            text="🌌 Skyblock",
            command=self.execute_skyblock,
            font=ctk.CTkFont(size=14),
            height=35,
            fg_color=self.colors["accent"],
            hover_color="#005A9E"
        )
        skyblock_btn.grid(row=1, column=1, padx=(5, 15), pady=5, sticky="ew")

        # Audio controls
        test_beep_btn = ctk.CTkButton(
            card,
            text="🔊 Test Beeping",
            command=self.test_beeping,
            font=ctk.CTkFont(size=12),
            height=30,
            fg_color=self.colors["secondary"],
            hover_color=self.colors["hover"]
        )
        test_beep_btn.grid(row=2, column=0, padx=(15, 5), pady=5, sticky="ew")

        stop_beep_btn = ctk.CTkButton(
            card,
            text="🔇 Stop Beeping",
            command=self.stop_beeping,
            font=ctk.CTkFont(size=12),
            height=30,
            fg_color=self.colors["warning"],
            hover_color="#E67E00"
        )
        stop_beep_btn.grid(row=2, column=1, padx=(5, 15), pady=(5, 15), sticky="ew")

    def setup_status_card(self, parent, row):
        """Setup real-time status card."""
        card = ctk.CTkFrame(parent, fg_color=self.colors["card_bg"])
        card.grid(row=row, column=0, sticky="ew", padx=5, pady=5)
        card.grid_columnconfigure(1, weight=1)

        # Card title
        title = ctk.CTkLabel(
            card,
            text="📊 Real-Time Status",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.colors["text_primary"]
        )
        title.grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10), sticky="w")

        # Status
        ctk.CTkLabel(
            card,
            text="Status:",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=self.colors["text_secondary"]
        ).grid(row=1, column=0, padx=(15, 10), pady=5, sticky="w")

        self.status_label = ctk.CTkLabel(
            card,
            text="🔴 Stopped",
            font=ctk.CTkFont(size=12),
            text_color=self.colors["error"]
        )
        self.status_label.grid(row=1, column=1, padx=(0, 15), pady=5, sticky="w")

        # Runtime
        ctk.CTkLabel(
            card,
            text="Runtime:",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=self.colors["text_secondary"]
        ).grid(row=2, column=0, padx=(15, 10), pady=5, sticky="w")

        self.runtime_label = ctk.CTkLabel(
            card,
            text="00:00:00",
            font=ctk.CTkFont(size=12),
            text_color=self.colors["text_primary"]
        )
        self.runtime_label.grid(row=2, column=1, padx=(0, 15), pady=5, sticky="w")

        # Position
        ctk.CTkLabel(
            card,
            text="Position:",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=self.colors["text_secondary"]
        ).grid(row=3, column=0, padx=(15, 10), pady=5, sticky="w")

        self.position_label = ctk.CTkLabel(
            card,
            text="0, 0, 0",
            font=ctk.CTkFont(size=12),
            text_color=self.colors["text_primary"]
        )
        self.position_label.grid(row=3, column=1, padx=(0, 15), pady=5, sticky="w")

        # Direction
        ctk.CTkLabel(
            card,
            text="Direction:",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=self.colors["text_secondary"]
        ).grid(row=4, column=0, padx=(15, 10), pady=(5, 15), sticky="w")

        self.direction_label = ctk.CTkLabel(
            card,
            text="None",
            font=ctk.CTkFont(size=12),
            text_color=self.colors["text_primary"]
        )
        self.direction_label.grid(row=4, column=1, padx=(0, 15), pady=(5, 15), sticky="w")

    def setup_statistics_tab(self):
        """Setup enhanced statistics tab."""
        tab = self.tabview.tab("📊 Statistics")
        tab.grid_columnconfigure(0, weight=1)
        tab.grid_rowconfigure(0, weight=1)

        # Scrollable content
        scroll_frame = ctk.CTkScrollableFrame(tab, fg_color="transparent")
        scroll_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        scroll_frame.grid_columnconfigure(0, weight=1)

        # Session statistics card
        session_card = ctk.CTkFrame(scroll_frame, fg_color=self.colors["card_bg"])
        session_card.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        session_card.grid_columnconfigure(1, weight=1)

        ctk.CTkLabel(
            session_card,
            text="📈 Current Session",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.colors["text_primary"]
        ).grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10), sticky="w")

        # Session stats labels
        self.session_stats_labels = {}
        stats_items = [
            ("Blocks Mined:", "blocks_mined"),
            ("Direction Changes:", "direction_changes"),
            ("Distance Traveled:", "distance_traveled"),
            ("Emergency Stops:", "emergency_stops"),
            ("Pause Count:", "pause_count")
        ]

        for i, (label_text, key) in enumerate(stats_items, 1):
            ctk.CTkLabel(
                session_card,
                text=label_text,
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=self.colors["text_secondary"]
            ).grid(row=i, column=0, padx=(15, 10), pady=5, sticky="w")

            self.session_stats_labels[key] = ctk.CTkLabel(
                session_card,
                text="0",
                font=ctk.CTkFont(size=12),
                text_color=self.colors["text_primary"]
            )
            self.session_stats_labels[key].grid(row=i, column=1, padx=(0, 15), pady=5, sticky="w")

        # Overall statistics card
        overall_card = ctk.CTkFrame(scroll_frame, fg_color=self.colors["card_bg"])
        overall_card.grid(row=1, column=0, sticky="ew", padx=5, pady=5)
        overall_card.grid_columnconfigure(1, weight=1)

        ctk.CTkLabel(
            overall_card,
            text="🏆 All-Time Statistics",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.colors["text_primary"]
        ).grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10), sticky="w")

        # Overall stats labels
        self.overall_stats_labels = {}
        overall_items = [
            ("Total Runtime:", "total_runtime_seconds"),
            ("Total Sessions:", "total_sessions"),
            ("Total Blocks:", "total_blocks_mined"),
            ("Total Distance:", "total_distance_traveled"),
            ("Total Emergencies:", "total_emergency_stops")
        ]

        for i, (label_text, key) in enumerate(overall_items, 1):
            ctk.CTkLabel(
                overall_card,
                text=label_text,
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=self.colors["text_secondary"]
            ).grid(row=i, column=0, padx=(15, 10), pady=5, sticky="w")

            self.overall_stats_labels[key] = ctk.CTkLabel(
                overall_card,
                text="0",
                font=ctk.CTkFont(size=12),
                text_color=self.colors["text_primary"]
            )
            self.overall_stats_labels[key].grid(row=i, column=1, padx=(0, 15), pady=(5, 15) if i == len(overall_items) else 5, sticky="w")

    def setup_settings_tab(self):
        """Setup settings and preferences tab."""
        tab = self.tabview.tab("⚙️ Settings")
        tab.grid_columnconfigure(0, weight=1)
        tab.grid_rowconfigure(0, weight=1)

        # Scrollable content
        scroll_frame = ctk.CTkScrollableFrame(tab, fg_color="transparent")
        scroll_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        scroll_frame.grid_columnconfigure(0, weight=1)

        # Preferences card
        prefs_card = ctk.CTkFrame(scroll_frame, fg_color=self.colors["card_bg"])
        prefs_card.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        prefs_card.grid_columnconfigure(1, weight=1)

        ctk.CTkLabel(
            prefs_card,
            text="🎛️ Preferences",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.colors["text_primary"]
        ).grid(row=0, column=0, columnspan=2, padx=15, pady=(15, 10), sticky="w")

        # Theme selection
        ctk.CTkLabel(
            prefs_card,
            text="Theme:",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=self.colors["text_secondary"]
        ).grid(row=1, column=0, padx=(15, 10), pady=5, sticky="w")

        self.theme_var = ctk.StringVar(value=self.settings.get("theme", "dark"))
        theme_menu = ctk.CTkOptionMenu(
            prefs_card,
            variable=self.theme_var,
            values=["dark", "light", "system"],
            command=self.change_theme,
            font=ctk.CTkFont(size=12)
        )
        theme_menu.grid(row=1, column=1, padx=(0, 15), pady=5, sticky="ew")

        # Confirmation dialogs
        self.confirm_var = ctk.BooleanVar(value=self.settings.get("confirm_actions", True))
        confirm_check = ctk.CTkCheckBox(
            prefs_card,
            text="Show confirmation dialogs",
            variable=self.confirm_var,
            command=self.save_current_settings,
            font=ctk.CTkFont(size=12)
        )
        confirm_check.grid(row=2, column=0, columnspan=2, padx=15, pady=5, sticky="w")

        # Auto-save statistics
        self.autosave_var = ctk.BooleanVar(value=self.settings.get("auto_save_stats", True))
        autosave_check = ctk.CTkCheckBox(
            prefs_card,
            text="Auto-save statistics",
            variable=self.autosave_var,
            command=self.save_current_settings,
            font=ctk.CTkFont(size=12)
        )
        autosave_check.grid(row=3, column=0, columnspan=2, padx=15, pady=5, sticky="w")

        # Show tooltips
        self.tooltips_var = ctk.BooleanVar(value=self.settings.get("show_tooltips", True))
        tooltips_check = ctk.CTkCheckBox(
            prefs_card,
            text="Show tooltips",
            variable=self.tooltips_var,
            command=self.save_current_settings,
            font=ctk.CTkFont(size=12)
        )
        tooltips_check.grid(row=4, column=0, columnspan=2, padx=15, pady=(5, 15), sticky="w")

    def setup_debug_tab(self):
        """Setup enhanced debug tab."""
        tab = self.tabview.tab("📋 Debug")
        tab.grid_columnconfigure(0, weight=1)
        tab.grid_rowconfigure(1, weight=1)

        # Debug controls
        controls_frame = ctk.CTkFrame(tab, fg_color=self.colors["card_bg"])
        controls_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=(10, 5))
        controls_frame.grid_columnconfigure(1, weight=1)

        ctk.CTkLabel(
            controls_frame,
            text="🐛 Debug Console",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.colors["text_primary"]
        ).grid(row=0, column=0, padx=15, pady=15, sticky="w")

        clear_btn = ctk.CTkButton(
            controls_frame,
            text="🗑️ Clear Log",
            command=self.clear_debug_log,
            font=ctk.CTkFont(size=12),
            height=30,
            width=100,
            fg_color=self.colors["warning"],
            hover_color="#E67E00"
        )
        clear_btn.grid(row=0, column=1, padx=15, pady=15, sticky="e")

        # Debug output
        self.debug_textbox = ctk.CTkTextbox(
            tab,
            font=ctk.CTkFont(family="Consolas", size=11),
            wrap="word",
            fg_color=self.colors["primary"],
            text_color=self.colors["text_primary"]
        )
        self.debug_textbox.grid(row=1, column=0, sticky="nsew", padx=10, pady=(0, 10))

    def setup_status_bar(self):
        """Setup status bar at bottom of window."""
        status_frame = ctk.CTkFrame(self, fg_color=self.colors["primary"], height=30)
        status_frame.grid(row=2, column=0, sticky="ew", padx=0, pady=0)
        status_frame.grid_columnconfigure(1, weight=1)
        status_frame.grid_propagate(False)

        # Connection status
        self.connection_label = ctk.CTkLabel(
            status_frame,
            text="🔗 Minescript: Connected" if MINESCRIPT_AVAILABLE else "❌ Minescript: Disconnected",
            font=ctk.CTkFont(size=10),
            text_color=self.colors["success"] if MINESCRIPT_AVAILABLE else self.colors["error"]
        )
        self.connection_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")

        # Quick stats
        self.quick_stats_label = ctk.CTkLabel(
            status_frame,
            text="Ready to mine",
            font=ctk.CTkFont(size=10),
            text_color=self.colors["text_muted"]
        )
        self.quick_stats_label.grid(row=0, column=1, padx=10, pady=5, sticky="e")

    def setup_tooltips(self):
        """Setup tooltips for better user experience."""
        if not self.settings.get("show_tooltips", True):
            return

        # Note: CustomTkinter doesn't have built-in tooltips
        # This would require a custom tooltip implementation
        pass

    def setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts."""
        self.bind("<Control-s>", lambda e: self.toggle_mining())
        self.bind("<Control-p>", lambda e: self.toggle_pause())
        self.bind("<Control-e>", lambda e: self.emergency_stop())
        self.bind("<Control-q>", lambda e: self.on_closing())

    # Core functionality methods
    def toggle_mining(self):
        """Toggle mining on/off with confirmation if enabled."""
        if not STATE["running"]:
            if self.confirm_var.get():
                if not messagebox.askyesno("Confirm", "Start mining operation?"):
                    return
            self.start_mining()
        else:
            if self.confirm_var.get():
                if not messagebox.askyesno("Confirm", "Stop mining operation?"):
                    return
            self.stop_mining()

    def start_mining(self):
        """Start the mining process with enhanced feedback."""
        STATE["running"] = True
        STATE["paused"] = False
        STATE["emergency_stop"] = False
        STATE["start_time"] = datetime.now()
        STATE["pause_state_locked"] = False

        # Reset session statistics
        STATE["session_stats"] = {
            "blocks_mined": 0,
            "direction_changes": 0,
            "distance_traveled": 0,
            "emergency_stops": 0,
            "pause_count": 0
        }

        # Start mining thread
        STATE["mining_thread"] = threading.Thread(target=mining_loop, daemon=True)
        STATE["mining_thread"].start()

        # Update UI
        self.start_stop_btn.configure(
            text="⏹️ Stop Mining",
            fg_color=self.colors["error"],
            hover_color="#B12B2F"
        )
        self.pause_resume_btn.configure(state="normal")
        self.status_indicator.configure(text_color=self.colors["success"])
        self.status_label.configure(text="🟢 Running", text_color=self.colors["success"])

        echo("§a[Miner] Mining started!")
        debug_log("§a[Miner] Mining started!")

    def stop_mining(self):
        """Stop the mining process with statistics saving."""
        # Calculate session stats
        session_runtime = 0
        if STATE["start_time"]:
            session_runtime = (datetime.now() - STATE["start_time"]).total_seconds()

        STATE["running"] = False
        STATE["paused"] = False
        STATE["pause_state_locked"] = False

        # Wait for thread to finish
        if STATE["mining_thread"] and STATE["mining_thread"].is_alive():
            STATE["mining_thread"].join(timeout=2)

        stop_all_movements()

        # Save session to history
        if session_runtime > 0:
            session_data = {
                "timestamp": datetime.now().isoformat(),
                "profile": CURRENT_PROFILE,
                "runtime_seconds": session_runtime,
                "stats": STATE["session_stats"].copy()
            }
            save_session_history(session_data)

            # Update overall stats
            if self.autosave_var.get():
                self.update_overall_stats(session_runtime)

        # Update UI
        self.start_stop_btn.configure(
            text="▶️ Start Mining",
            fg_color=self.colors["success"],
            hover_color="#0E6B0E"
        )
        self.pause_resume_btn.configure(text="⏸️ Pause", state="disabled")
        self.status_indicator.configure(text_color=self.colors["error"])
        self.status_label.configure(text="🔴 Stopped", text_color=self.colors["error"])

        echo("§c[Miner] Mining stopped!")
        debug_log("§c[Miner] Mining stopped!")

    def toggle_pause(self):
        """Toggle pause/resume with enhanced feedback."""
        if not STATE["paused"]:
            STATE["paused"] = True
            STATE["pause_state_locked"] = False
            STATE["session_stats"]["pause_count"] += 1
            self.pause_resume_btn.configure(text="▶️ Resume")
            self.status_indicator.configure(text_color=self.colors["warning"])
            self.status_label.configure(text="🟡 Paused", text_color=self.colors["warning"])
            echo("§e[Miner] Mining paused")
            debug_log("§e[Miner] Mining paused")
        else:
            STATE["paused"] = False
            STATE["pause_state_locked"] = False
            self.pause_resume_btn.configure(text="⏸️ Pause")
            self.status_indicator.configure(text_color=self.colors["success"])
            self.status_label.configure(text="🟢 Running", text_color=self.colors["success"])
            echo("§a[Miner] Mining resumed")
            debug_log("§a[Miner] Mining resumed")

    def emergency_stop(self):
        """Emergency stop with immediate response."""
        STATE["emergency_stop"] = True
        STATE["running"] = False
        STATE["paused"] = False
        STATE["pause_state_locked"] = False
        STATE["session_stats"]["emergency_stops"] += 1

        stop_all_movements()

        # Update UI
        self.start_stop_btn.configure(
            text="▶️ Start Mining",
            fg_color=self.colors["success"],
            hover_color="#0E6B0E"
        )
        self.pause_resume_btn.configure(text="⏸️ Pause", state="disabled")
        self.status_indicator.configure(text_color=self.colors["error"])
        self.status_label.configure(text="🛑 Emergency Stop", text_color=self.colors["error"])

        echo("§c[Miner] EMERGENCY STOP ACTIVATED!")
        debug_log("§c[Miner] EMERGENCY STOP ACTIVATED!")

    # Utility methods
    def change_profile(self, selected_profile):
        """Change the active mining profile with confirmation."""
        global CURRENT_PROFILE, CONFIG

        if self.confirm_var.get() and STATE["running"]:
            if not messagebox.askyesno("Confirm", f"Change profile to {selected_profile}?\nThis will stop current mining."):
                self.profile_var.set(CURRENT_PROFILE)  # Reset dropdown
                return

        if selected_profile in PROFILES:
            # Stop mining if running
            if STATE["running"]:
                self.stop_mining()

            CURRENT_PROFILE = selected_profile
            CONFIG = get_config()
            profile_name = CONFIG["name"]

            # Update UI
            self.profile_indicator.configure(text=f"Profile: {profile_name}")
            self.profile_desc_label.configure(text=f"📝 {profile_name}")

            # Save to settings
            self.settings["last_profile"] = selected_profile
            save_settings(self.settings)

            echo(f"§a[Miner] Profile changed to: {profile_name}")
            debug_log(f"§a[Miner] Profile changed to: {profile_name}")
        else:
            echo(f"§c[Miner] Invalid profile: {selected_profile}")

    def execute_home_gen(self):
        """Execute profile-specific home command."""
        try:
            config = get_config()
            home_command = config["home_command"]
            execute(home_command)
            echo(f"§a[Miner] Executed: {home_command}")
            debug_log(f"§a[Miner] Executed: {home_command}")
        except Exception as e:
            echo(f"§c[Miner] Error executing home command: {e}")
            debug_log(f"§c[Miner] Home command error: {e}")

    def execute_skyblock(self):
        """Execute /skyblock command."""
        try:
            execute("/skyblock")
            echo("§a[Miner] Executed: /skyblock")
            debug_log("§a[Miner] Executed: /skyblock")
        except Exception as e:
            echo(f"§c[Miner] Error executing /skyblock: {e}")
            debug_log(f"§c[Miner] Skyblock command error: {e}")

    def test_beeping(self):
        """Test the beeping sound."""
        if not STATE["beeping_active"]:
            start_beeping()
            echo("§e[AUDIO] Test beeping started - use 'Stop Beeping' to stop")
            debug_log("§e[AUDIO] Test beeping started")
        else:
            echo("§e[AUDIO] Beeping already active")

    def stop_beeping(self):
        """Stop the beeping sound."""
        stop_beeping()
        echo("§e[AUDIO] Beeping stopped")
        debug_log("§e[AUDIO] Beeping stopped")

    def clear_debug_log(self):
        """Clear the debug log textbox."""
        try:
            self.debug_textbox.delete("1.0", "end")
            timestamp = datetime.now().strftime('%H:%M:%S')
            self.debug_textbox.insert("1.0", f"[{timestamp}] Debug log cleared.\n")
        except Exception as e:
            echo(f"§c[DEBUG] Error clearing debug log: {e}")

    def change_theme(self, theme):
        """Change application theme."""
        ctk.set_appearance_mode(theme)
        self.settings["theme"] = theme
        save_settings(self.settings)
        debug_log(f"§a[GUI] Theme changed to: {theme}")

    def save_current_settings(self):
        """Save current settings to file."""
        self.settings["confirm_actions"] = self.confirm_var.get()
        self.settings["auto_save_stats"] = self.autosave_var.get()
        self.settings["show_tooltips"] = self.tooltips_var.get()
        save_settings(self.settings)

    def update_overall_stats(self, session_runtime):
        """Update overall statistics with session data."""
        stats = load_stats()
        stats["total_runtime_seconds"] += session_runtime
        stats["total_sessions"] += 1

        # Add session stats to overall
        for key, value in STATE["session_stats"].items():
            overall_key = f"total_{key}"
            if overall_key in stats:
                stats[overall_key] += value

        # Track profile usage
        if "profiles_used" not in stats:
            stats["profiles_used"] = {}
        if CURRENT_PROFILE not in stats["profiles_used"]:
            stats["profiles_used"][CURRENT_PROFILE] = 0
        stats["profiles_used"][CURRENT_PROFILE] += 1

        save_stats(stats)
        self.stats = stats

    # Update loops
    def update_stats_loop(self):
        """Update statistics display periodically."""
        try:
            # Update session statistics
            for key, label in self.session_stats_labels.items():
                value = STATE["session_stats"].get(key, 0)
                if key == "distance_traveled":
                    label.configure(text=f"{value:.1f}m")
                else:
                    label.configure(text=str(int(value)))

            # Update overall statistics
            for key, label in self.overall_stats_labels.items():
                value = self.stats.get(key, 0)
                if key == "total_runtime_seconds":
                    label.configure(text=format_time_from_seconds(value))
                elif key == "total_distance_traveled":
                    label.configure(text=f"{value:.1f}m")
                else:
                    label.configure(text=str(int(value)))

            # Update runtime
            if STATE["running"] and STATE["start_time"]:
                runtime = (datetime.now() - STATE["start_time"]).total_seconds()
                self.runtime_label.configure(text=format_time_from_seconds(runtime))

            # Update position and direction
            try:
                pos = player_position()
                if pos and len(pos) >= 3:
                    self.position_label.configure(text=f"{pos[0]:.1f}, {pos[1]:.1f}, {pos[2]:.1f}")
            except:
                pass

            direction = STATE.get("current_direction", "None")
            self.direction_label.configure(text=direction)

            # Update quick stats in status bar
            if STATE["running"]:
                blocks = int(STATE["session_stats"]["blocks_mined"])
                runtime = (datetime.now() - STATE["start_time"]).total_seconds() if STATE["start_time"] else 0
                self.quick_stats_label.configure(text=f"Blocks: {blocks} | Runtime: {format_time_from_seconds(runtime)}")
            else:
                self.quick_stats_label.configure(text="Ready to mine")

        except Exception as e:
            debug_log(f"§c[GUI] Stats update error: {e}")

        # Schedule next update
        self.after(500, self.update_stats_loop)

    def update_debug_panel(self):
        """Update the debug panel with new messages."""
        try:
            messages_added = 0
            while not STATE["debug_queue"].empty() and messages_added < 10:
                try:
                    message = STATE["debug_queue"].get_nowait()
                    self.debug_textbox.insert("end", f"{message}\n")
                    messages_added += 1
                except:
                    break

            if messages_added > 0:
                self.debug_textbox.see("end")

        except Exception as e:
            print(f"Error updating debug panel: {e}")

        # Schedule next update
        self.after(100, self.update_debug_panel)

    def on_closing(self):
        """Handle window closing with cleanup."""
        # Save window geometry
        self.settings["window_geometry"] = self.geometry()
        save_settings(self.settings)

        # Stop mining if running
        if STATE["running"]:
            if self.confirm_var.get():
                if messagebox.askyesno("Confirm", "Mining is active. Stop and exit?"):
                    self.stop_mining()
                else:
                    return
            else:
                self.stop_mining()

        # Stop beeping
        stop_beeping()

        # Destroy window
        self.destroy()

# Main execution
def main():
    """Main function to run the mining bot."""
    try:
        app = ModernMinerGUI()
        app.mainloop()
    except Exception as e:
        print(f"Error running mining bot: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
