#!/usr/bin/env python3
"""
Minescript 4.0 Mining Automation Script
======================================

This script provides automated mining with lateral (left/right) movement while continuously attacking.
Features:
- Continuous attack while moving left and right between X coordinates -40 to +39
- Tkinter GUI with Start, Stop, and Emergency Stop controls
- Safety system that checks for minecraft:white_concrete floor underneath the player
- Graceful error handling and status feedback

Usage: \\mining_automation
"""

import minescript
import customtkinter as ctk
from tkinter import messagebox
import threading
import time
import sys
import traceback
import math
import winsound


def extract_base_block_name(block_string):
    """
    Extract the base block name from a full Minecraft block string.

    Examples:
    - 'minecraft:iron_bars[east=false,north=false,south=false,waterlogged=false,west=true]'
      -> 'minecraft:iron_bars'
    - 'minecraft:stone' -> 'minecraft:stone'
    - 'air' -> 'air'

    Args:
        block_string (str): Full block string from getblock()

    Returns:
        str: Base block name without state properties
    """
    if not block_string:
        return block_string

    # Find the first '[' character which indicates start of block states
    bracket_index = block_string.find('[')

    if bracket_index == -1:
        # No block states, return as-is
        return block_string
    else:
        # Extract everything before the '[' character
        return block_string[:bracket_index]


class MiningAutomation:
    def __init__(self):
        # Automation state
        self.running = False
        self.emergency_stop = False
        self.direction = 1  # 1 for positive X, -1 for negative X
        self.automation_thread = None

        # Boundaries - Updated for lateral mining
        self.min_x = -40
        self.max_x = 39

        # Safety settings - Floor must be white concrete
        self.safety_blocks = [
            "minecraft:white_concrete"
        ]
        self.safety_check_interval = 0.5  # Check every 500ms

        # Performance settings
        self.movement_delay = 0.05  # Delay between movement checks
        self.transition_delay = 0.1  # Delay during direction changes

        # Error handling settings
        self.max_position_errors = 10
        self.stuck_threshold = 5.0  # seconds before considering player stuck
        self.stuck_movement_threshold = 0.1  # minimum movement to not be considered stuck

        # GUI components
        self.root = None
        self.status_var = None
        self.position_var = None

        # Runtime statistics
        self.start_time = None
        self.direction_changes = 0

        # Alarm system
        self.alarm_active = False
        self.alarm_thread = None
        self.safety_violation_time = None

        # Initialize GUI
        self.setup_gui()
    
    def setup_gui(self):
        """Initialize the CustomTkinter GUI with resizable and scrollable features"""
        # Set CustomTkinter appearance mode and color theme
        ctk.set_appearance_mode("dark")  # "dark" or "light"
        ctk.set_default_color_theme("blue")  # "blue", "green", "dark-blue"

        self.root = ctk.CTk()
        self.root.title("Minescript Mining Automation")

        # Set default size and make window resizable
        self.root.geometry("500x600")
        self.root.minsize(400, 400)  # Minimum window size for usability
        self.root.resizable(True, True)  # Allow both horizontal and vertical resizing

        # Configure grid weight for responsive layout
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_rowconfigure(0, weight=1)

        # Create main scrollable frame
        self.scrollable_frame = ctk.CTkScrollableFrame(
            self.root,
            label_text="Mining Automation Control",
            label_font=ctk.CTkFont(size=18, weight="bold")
        )
        self.scrollable_frame.grid(row=0, column=0, sticky="nsew", padx=20, pady=20)

        # Configure scrollable frame grid
        self.scrollable_frame.grid_columnconfigure(0, weight=1)
        
        # Status section (now inside scrollable frame)
        status_frame = ctk.CTkFrame(self.scrollable_frame)
        status_frame.grid(row=0, column=0, sticky="ew", pady=(10, 15), padx=10)
        status_frame.grid_columnconfigure(0, weight=1)

        status_title = ctk.CTkLabel(status_frame, text="Status",
                                   font=ctk.CTkFont(size=14, weight="bold"))
        status_title.grid(row=0, column=0, pady=(15, 5))

        self.status_var = ctk.StringVar(value="Ready")
        status_label = ctk.CTkLabel(status_frame, textvariable=self.status_var)
        status_label.grid(row=1, column=0, pady=3)

        self.position_var = ctk.StringVar(value="Position: Not available")
        position_label = ctk.CTkLabel(status_frame, textvariable=self.position_var)
        position_label.grid(row=2, column=0, pady=3)

        # Statistics display
        self.stats_var = ctk.StringVar(value="Runtime: 0s | Direction changes: 0")
        stats_label = ctk.CTkLabel(status_frame, textvariable=self.stats_var)
        stats_label.grid(row=3, column=0, pady=(3, 15))
        
        # Control buttons section
        button_frame = ctk.CTkFrame(self.scrollable_frame)
        button_frame.grid(row=1, column=0, sticky="ew", pady=(0, 15), padx=10)
        button_frame.grid_columnconfigure((0, 1, 2), weight=1)

        button_title = ctk.CTkLabel(button_frame, text="Controls",
                                   font=ctk.CTkFont(size=14, weight="bold"))
        button_title.grid(row=0, column=0, columnspan=3, pady=(15, 10))

        # Buttons in a responsive grid layout
        self.start_button = ctk.CTkButton(button_frame, text="Start Mining",
                                         command=self.start_automation,
                                         font=ctk.CTkFont(weight="bold"))
        self.start_button.grid(row=1, column=0, padx=(15, 5), pady=(0, 15), sticky="ew")

        self.stop_button = ctk.CTkButton(button_frame, text="Stop Mining",
                                        command=self.stop_automation,
                                        state="disabled",
                                        font=ctk.CTkFont(weight="bold"))
        self.stop_button.grid(row=1, column=1, padx=5, pady=(0, 15), sticky="ew")

        self.emergency_button = ctk.CTkButton(button_frame, text="EMERGENCY STOP",
                                             command=self.emergency_stop_automation,
                                             fg_color="#dc2626", hover_color="#b91c1c",
                                             font=ctk.CTkFont(weight="bold"))
        self.emergency_button.grid(row=1, column=2, padx=(5, 15), pady=(0, 15), sticky="ew")

        # Stop Alarm button
        self.stop_alarm_button = ctk.CTkButton(button_frame, text="Stop Alarm",
                                              command=self.stop_alarm,
                                              fg_color="#f59e0b", hover_color="#d97706",
                                              font=ctk.CTkFont(weight="bold"))
        self.stop_alarm_button.grid(row=2, column=0, columnspan=3, padx=15, pady=(0, 15), sticky="ew")
        
        # Settings section
        settings_frame = ctk.CTkFrame(self.scrollable_frame)
        settings_frame.grid(row=2, column=0, sticky="ew", pady=(0, 15), padx=10)
        settings_frame.grid_columnconfigure(0, weight=1)

        settings_title = ctk.CTkLabel(settings_frame, text="Settings",
                                     font=ctk.CTkFont(size=14, weight="bold"))
        settings_title.grid(row=0, column=0, pady=(15, 5))

        boundaries_label = ctk.CTkLabel(settings_frame, text=f"X Boundaries: {self.min_x} to {self.max_x}")
        boundaries_label.grid(row=1, column=0, pady=3)

        # Format safety blocks list for display
        safety_blocks_display = ", ".join([block.replace("minecraft:", "") for block in self.safety_blocks])
        safety_label = ctk.CTkLabel(settings_frame, text=f"Safety Blocks: {safety_blocks_display}")
        safety_label.grid(row=2, column=0, pady=(3, 15))

        # Info section
        info_frame = ctk.CTkFrame(self.scrollable_frame)
        info_frame.grid(row=3, column=0, sticky="ew", pady=(0, 15), padx=10)
        info_frame.grid_columnconfigure(0, weight=1)

        info_title = ctk.CTkLabel(info_frame, text="Information",
                                 font=ctk.CTkFont(size=14, weight="bold"))
        info_title.grid(row=0, column=0, pady=(15, 5))

        info_text = ("This script moves the player continuously right along the X-axis\n"
                    "while continuously mining. When reaching the left boundary (-40),\n"
                    "it teleports to /home mine1 and resumes rightward mining.\n"
                    "Enhanced safety system with audio alarms and 2-second delay.")
        info_label = ctk.CTkLabel(info_frame, text=info_text, justify="left")
        info_label.grid(row=1, column=0, pady=(3, 15))

        # Additional help section to demonstrate scrolling
        help_frame = ctk.CTkFrame(self.scrollable_frame)
        help_frame.grid(row=4, column=0, sticky="ew", pady=(0, 15), padx=10)
        help_frame.grid_columnconfigure(0, weight=1)

        help_title = ctk.CTkLabel(help_frame, text="Usage Instructions",
                                 font=ctk.CTkFont(size=14, weight="bold"))
        help_title.grid(row=0, column=0, pady=(15, 5))

        help_text = ("1. Set up /home mine1 teleport location before starting\n"
                    "2. Position yourself on white concrete within mining area\n"
                    "3. Click 'Start Mining' to begin unidirectional mining automation\n"
                    "4. Use 'Stop Mining' for graceful shutdown\n"
                    "5. Use 'EMERGENCY STOP' for immediate halt\n"
                    "6. Use 'Stop Alarm' to silence safety alarms\n"
                    "7. Monitor the status display for real-time information\n\n"
                    "Mining Pattern:\n"
                    "• Continuous rightward movement (unidirectional)\n"
                    "• At left boundary (-40): Teleports to /home mine1 and resumes\n"
                    "• No direction reversal - always moves right\n"
                    "• Maintains continuous mining during teleportation\n\n"
                    "Enhanced Safety Features:\n"
                    "• Continuous white concrete floor detection\n"
                    "• Audio alarm system for safety violations\n"
                    "• 2-second mining continuation after violation detection\n"
                    "• Manual alarm stop control\n"
                    "• Stuck detection and automatic unstuck attempts\n\n"
                    "Troubleshooting:\n"
                    "• Ensure /home mine1 is set up before starting\n"
                    "• Use 'Stop Alarm' button to silence safety alarms\n"
                    "• Check chat for error messages and debug output\n"
                    "• Ensure you're standing on white concrete floor")
        help_label = ctk.CTkLabel(help_frame, text=help_text, justify="left")
        help_label.grid(row=1, column=0, pady=(3, 15))

        # Window resize tips section
        tips_frame = ctk.CTkFrame(self.scrollable_frame)
        tips_frame.grid(row=5, column=0, sticky="ew", pady=(0, 20), padx=10)
        tips_frame.grid_columnconfigure(0, weight=1)

        tips_title = ctk.CTkLabel(tips_frame, text="GUI Tips",
                                 font=ctk.CTkFont(size=14, weight="bold"))
        tips_title.grid(row=0, column=0, pady=(15, 5))

        tips_text = ("• This window is resizable - drag corners to adjust size\n"
                    "• Use mouse wheel to scroll through content\n"
                    "• Minimum window size: 400x400 pixels\n"
                    "• All buttons scale with window width\n"
                    "• Status updates in real-time during operation")
        tips_label = ctk.CTkLabel(tips_frame, text=tips_text, justify="left")
        tips_label.grid(row=1, column=0, pady=(3, 15))

        # Configure scrollable frame to expand properly
        self.scrollable_frame.grid_columnconfigure(0, weight=1)

        # Bind window resize event for responsive behavior
        self.root.bind("<Configure>", self.on_window_resize)

        # Start position update timer
        self.update_position_display()

    def on_window_resize(self, event):
        """Handle window resize events to maintain responsive layout"""
        # Only handle resize events for the main window, not child widgets
        if event.widget == self.root:
            # Get current window size
            width = self.root.winfo_width()
            height = self.root.winfo_height()

            # Ensure minimum size constraints
            if width < 400:
                self.root.geometry(f"400x{height}")
            if height < 400:
                self.root.geometry(f"{width}x400")

            # Update scrollable frame to use available space efficiently
            # The CTkScrollableFrame automatically handles content scaling
    
    def update_position_display(self):
        """Update the position display and statistics in the GUI"""
        try:
            if not self.emergency_stop:
                pos = minescript.player_position()
                self.position_var.set(f"Position: X={pos[0]:.1f}, Y={pos[1]:.1f}, Z={pos[2]:.1f}")

                # Update statistics if running
                if self.running and self.start_time:
                    runtime = int(time.time() - self.start_time)
                    self.stats_var.set(f"Runtime: {runtime}s | Direction changes: {self.direction_changes}")
                else:
                    self.stats_var.set(f"Runtime: 0s | Direction changes: {self.direction_changes}")
        except Exception as e:
            self.position_var.set(f"Position: Error - {str(e)}")

        # Schedule next update
        self.root.after(1000, self.update_position_display)
    
    def check_safety_condition(self, debug=False):
        """Check if the floor block underneath the player is white concrete"""
        try:
            pos = minescript.player_position()

            # Calculate block coordinates with proper rounding
            # Player position is at feet level, so we need to check the block below
            player_x = pos[0]
            player_y = pos[1]
            player_z = pos[2]

            # Convert to block coordinates - use floor for proper block alignment
            block_x = math.floor(player_x)
            block_y = math.floor(player_y) - 1  # Block underneath
            block_z = math.floor(player_z)

            if debug:
                minescript.echo(f"DEBUG: Player position: X={player_x:.3f}, Y={player_y:.3f}, Z={player_z:.3f}")
                minescript.echo(f"DEBUG: Checking block at: X={block_x}, Y={block_y}, Z={block_z}")

            # Get the block at the calculated position
            block_below_full = minescript.getblock(block_x, block_y, block_z)

            # Extract base block name (remove block state properties)
            block_below_base = extract_base_block_name(block_below_full)

            # Check if the block matches any of the allowed safety blocks
            is_safe_block = False
            for safety_block in self.safety_blocks:
                expected_base = extract_base_block_name(safety_block)
                if block_below_base == expected_base:
                    is_safe_block = True
                    break

            if debug:
                minescript.echo(f"DEBUG: Full block detected: '{block_below_full}'")
                minescript.echo(f"DEBUG: Base block name: '{block_below_base}'")
                minescript.echo(f"DEBUG: Allowed safety blocks: {self.safety_blocks}")
                minescript.echo(f"DEBUG: Safety check result: {is_safe_block}")

                # Show which blocks would match
                minescript.echo("DEBUG: Block matching results:")
                for safety_block in self.safety_blocks:
                    expected_base = extract_base_block_name(safety_block)
                    matches = block_below_base == expected_base
                    minescript.echo(f"  '{safety_block}' -> '{expected_base}': {matches}")

                # Also check surrounding blocks for context
                minescript.echo("DEBUG: Surrounding blocks:")
                for dx in [-1, 0, 1]:
                    for dz in [-1, 0, 1]:
                        try:
                            surrounding_block_full = minescript.getblock(block_x + dx, block_y, block_z + dz)
                            surrounding_block_base = extract_base_block_name(surrounding_block_full)
                            minescript.echo(f"  [{dx:+2},{dz:+2}]: {surrounding_block_base}")
                            if debug and dx == 0 and dz == 0:  # Show full string for center block
                                minescript.echo(f"    Full: {surrounding_block_full}")
                        except:
                            minescript.echo(f"  [{dx:+2},{dz:+2}]: ERROR")

            # Return true if block matches any allowed safety block
            return is_safe_block

        except Exception as e:
            minescript.echo(f"Safety check error: {e}")
            if debug:
                import traceback
                minescript.echo(f"DEBUG: Full traceback: {traceback.format_exc()}")
            return False
    
    def start_automation(self):
        """Start the mining automation"""
        if self.running:
            return
        
        # Initial safety check with debug output
        minescript.echo("Performing initial safety check...")
        if not self.check_safety_condition(debug=True):
            safety_blocks_display = ", ".join([block.replace("minecraft:", "") for block in self.safety_blocks])
            messagebox.showerror("Safety Check Failed",
                               f"The block underneath the player is not one of the allowed safety blocks.\n"
                               f"Allowed blocks: {safety_blocks_display}\n"
                               "Please move to a safe location before starting.\n"
                               "Check the chat for detailed debug information.")
            return
        
        self.running = True
        self.emergency_stop = False

        # Initialize statistics
        self.start_time = time.time()
        self.direction_changes = 0

        # Update GUI
        self.start_button.configure(state="disabled")
        self.stop_button.configure(state="normal")
        self.status_var.set("Starting automation...")

        # Start automation thread
        self.automation_thread = threading.Thread(target=self.automation_loop, daemon=True)
        self.automation_thread.start()

        minescript.echo("Mining automation started!")
    
    def stop_automation(self):
        """Stop the mining automation gracefully"""
        if not self.running:
            return
        
        self.running = False
        self.status_var.set("Stopping...")
        
        # Wait for thread to finish
        if self.automation_thread and self.automation_thread.is_alive():
            self.automation_thread.join(timeout=2.0)
        
        self.cleanup_automation()
        minescript.echo("Mining automation stopped.")
    
    def emergency_stop_automation(self):
        """Emergency stop - immediately halt all operations"""
        self.emergency_stop = True
        self.running = False
        
        self.cleanup_automation()
        self.status_var.set("EMERGENCY STOPPED")
        
        minescript.echo("EMERGENCY STOP ACTIVATED!")
        messagebox.showwarning("Emergency Stop", "All automation has been immediately halted!")
    
    def cleanup_automation(self):
        """Clean up automation state and GUI"""
        # Stop alarm if active
        self.stop_alarm()
        self.safety_violation_time = None

        # Stop all player actions
        try:
            minescript.player_press_attack(False)
            minescript.player_press_left(False)
            minescript.player_press_right(False)
        except Exception as e:
            minescript.echo(f"Cleanup error: {e}")

        # Update GUI
        self.start_button.configure(state="normal")
        self.stop_button.configure(state="disabled")

        if not self.emergency_stop:
            self.status_var.set("Ready")
    
    def automation_loop(self):
        """Main automation loop with enhanced error handling"""
        position_error_count = 0
        last_position = None
        stuck_check_time = time.time()

        try:
            self.status_var.set("Running - Mining active")

            # Start continuous attacking and rightward movement immediately
            minescript.player_press_attack(True)
            minescript.player_press_right(True)
            minescript.echo("Started continuous mining and rightward movement")

            last_safety_check = time.time()
            last_attack_check = time.time()
            attack_check_interval = 5.0  # Check attack state every 5 seconds

            while self.running and not self.emergency_stop:
                # Periodic safety check
                current_time = time.time()
                if current_time - last_safety_check >= self.safety_check_interval:
                    if not self.check_safety_condition():
                        if self.safety_violation_time is None:
                            # First safety violation detected
                            self.safety_violation_time = current_time
                            self.status_var.set("SAFETY VIOLATION - Alarm activated, stopping in 2 seconds")
                            minescript.echo("SAFETY VIOLATION: Block underneath is not white concrete!")
                            minescript.echo("Continuing mining for 2 seconds before stopping...")
                            self.start_alarm()
                            # Ensure continuous mining during violation period
                            minescript.player_press_attack(True)
                            minescript.player_press_right(True)
                        else:
                            # Still within 2-second violation period - maintain continuous mining
                            minescript.player_press_attack(True)
                            minescript.player_press_right(True)

                        if current_time - self.safety_violation_time >= 2.0:
                            # 2 seconds have passed since violation
                            self.status_var.set("SAFETY VIOLATION - Stopped")
                            minescript.echo("2 seconds elapsed - stopping all operations")
                            self.emergency_stop_automation()
                            return
                    else:
                        # Safety condition is good, reset violation timer
                        if self.safety_violation_time is not None:
                            self.safety_violation_time = None
                            self.stop_alarm()
                            minescript.echo("Safety condition restored")
                    last_safety_check = current_time

                # Periodic attack state monitoring and reinforcement
                if current_time - last_attack_check >= attack_check_interval:
                    minescript.echo("DEBUG: Reinforcing continuous attack state")
                    minescript.player_press_attack(True)
                    minescript.player_press_right(True)
                    last_attack_check = current_time

                # Get current position with error handling
                try:
                    pos = minescript.player_position()
                    current_x = pos[0]
                    position_error_count = 0  # Reset error count on success

                    # Check if player is stuck (hasn't moved significantly)
                    if last_position is not None:
                        distance_moved = abs(current_x - last_position[0])
                        if distance_moved < self.stuck_movement_threshold:
                            if current_time - stuck_check_time > self.stuck_threshold:
                                minescript.echo("Player appears to be stuck! Attempting to unstuck...")
                                self.attempt_unstuck()
                                stuck_check_time = current_time
                                # Ensure attack state is restored after unstuck attempt
                                minescript.player_press_attack(True)
                                minescript.player_press_right(True)
                            else:
                                # Still detecting potential stuck - maintain attack state
                                minescript.player_press_attack(True)
                                minescript.player_press_right(True)
                        else:
                            stuck_check_time = current_time  # Reset stuck timer
                            # Ensure continuous attack state during normal movement
                            minescript.player_press_attack(True)
                            minescript.player_press_right(True)

                    last_position = pos

                except Exception as e:
                    position_error_count += 1
                    minescript.echo(f"Position error ({position_error_count}/{self.max_position_errors}): {e}")

                    if position_error_count >= self.max_position_errors:
                        minescript.echo("Too many position errors - stopping automation")
                        self.status_var.set("Connection issues - Stopped")
                        self.emergency_stop_automation()
                        return

                    time.sleep(0.5)  # Longer delay on error
                    continue

                # Validate position is reasonable
                if abs(current_x) > 1000:  # Sanity check for reasonable coordinates
                    minescript.echo(f"Warning: Player at unusual position X={current_x}")

                # Check boundaries - only left boundary teleportation
                if current_x <= self.min_x:
                    # Reached min X - Always teleport regardless of direction
                    # This handles cases where direction might have been changed unexpectedly
                    self.direction_changes += 1
                    minescript.echo(f"Reached min boundary ({self.min_x}), initiating teleportation sequence")

                    # Stop all lateral movement and attacking (synchronized stop)
                    minescript.player_press_left(False)
                    minescript.player_press_right(False)
                    minescript.player_press_attack(False)
                    self.status_var.set("Teleporting to mine1...")

                    # Wait 1000ms before teleporting
                    time.sleep(1.0)

                    # Execute teleportation command with error handling
                    try:
                        minescript.execute("/home mine1")
                        minescript.echo("Teleportation command executed: /home mine1")
                    except Exception as e:
                        minescript.echo(f"Teleportation failed: {e}")
                        # Continue mining even if teleport fails

                    # Wait additional 1000ms after teleport
                    time.sleep(1.0)

                    # Additional 1-second delay before resuming movement
                    time.sleep(1.0)

                    # Resume synchronized movement and attacking
                    self.direction = 1  # Change direction to move right
                    minescript.player_press_right(True)
                    minescript.player_press_attack(True)
                    self.status_var.set(f"Running - Moving right after teleport (X: {current_x:.1f})")
                    minescript.echo("Resumed mining after teleportation, moving right")

                    # Additional delay after teleportation to ensure player has moved away from boundary
                    # This prevents the teleportation logic from triggering again immediately
                    time.sleep(1.0)  # 1 second delay to allow teleportation to complete and movement to begin

                else:
                    # Continue moving right with continuous attacking (unidirectional movement)
                    self.direction = 1  # Always move right

                    # Ensure continuous attack and movement state (reinforcement)
                    minescript.player_press_right(True)
                    minescript.player_press_left(False)
                    minescript.player_press_attack(True)  # Continuous attack reinforcement

                    self.status_var.set(f"Running - Moving right & attacking (X: {current_x:.1f})")

                # Final attack state reinforcement before loop iteration ends
                minescript.player_press_attack(True)
                minescript.player_press_right(True)

                # Small delay to prevent excessive CPU usage
                time.sleep(self.movement_delay)

        except Exception as e:
            error_msg = f"Automation error: {e}"
            minescript.echo(error_msg)
            minescript.echo(f"Traceback: {traceback.format_exc()}")
            self.status_var.set("Error occurred - Stopped")
            self.emergency_stop_automation()

        finally:
            self.cleanup_automation()

    def attempt_unstuck(self):
        """Attempt to unstuck the player by jumping and brief direction changes"""
        try:

            # Try jumping
            minescript.player_press_jump(True)
            time.sleep(0.2)
            minescript.player_press_jump(False)

            # Brief opposite direction movement for lateral unstuck with synchronized attacking
            if self.direction == 1:
                # Currently moving right, stop attacking and try moving left briefly
                minescript.player_press_right(False)
                minescript.player_press_attack(False)  # Stop attacking when movement stops
                minescript.player_press_left(True)
                time.sleep(0.3)
                minescript.player_press_left(False)
                # Restore original right movement and attacking
                minescript.player_press_right(True)
                minescript.player_press_attack(True)  # Resume attacking with movement
            else:
                # Currently moving left, try moving right briefly
                minescript.player_press_left(False)
                minescript.player_press_right(True)
                minescript.player_press_attack(True)  # Attack when moving right
                time.sleep(0.3)
                minescript.player_press_right(False)
                minescript.player_press_attack(False)  # Stop attacking when right movement stops
                # Restore original left movement (no attacking for left movement)
                minescript.player_press_left(True)

        except Exception as e:
            minescript.echo(f"Error during unstuck attempt: {e}")

    def start_alarm(self):
        """Start the continuous safety alarm"""
        if not self.alarm_active:
            self.alarm_active = True
            self.alarm_thread = threading.Thread(target=self._alarm_loop, daemon=True)
            self.alarm_thread.start()
            minescript.echo("SAFETY ALARM ACTIVATED!")

    def stop_alarm(self):
        """Stop the safety alarm"""
        self.alarm_active = False
        if self.alarm_thread:
            self.alarm_thread = None
        minescript.echo("Safety alarm stopped")

    def _alarm_loop(self):
        """Continuous alarm loop"""
        while self.alarm_active:
            try:
                # Play alarm sound (frequency=1000Hz, duration=200ms)
                winsound.Beep(1000, 200)
                time.sleep(0.3)  # Brief pause between beeps
            except Exception as e:
                # If winsound fails, use echo as fallback
                minescript.echo("ALARM! SAFETY VIOLATION!")
                time.sleep(0.5)

    def run(self):
        """Start the GUI main loop"""
        try:
            minescript.echo("Mining Automation GUI started. Use the interface to control the automation.")
            self.root.mainloop()
        except KeyboardInterrupt:
            self.emergency_stop_automation()
        except Exception as e:
            minescript.echo(f"GUI error: {e}")
            self.emergency_stop_automation()


def main():
    """Main entry point"""
    try:
        # Create and run the automation
        automation = MiningAutomation()
        automation.run()
    except Exception as e:
        minescript.echo(f"Failed to start mining automation: {e}")
        minescript.echo(f"Traceback: {traceback.format_exc()}")


if __name__ == "__main__":
    main()
