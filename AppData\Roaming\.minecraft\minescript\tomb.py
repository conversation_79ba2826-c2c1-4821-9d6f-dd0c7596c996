import time
import re
import math
import threading
from datetime import datetime
import minescript
import customtkinter as ctk
from typing import List, Dict, Optional

# Configure customtkinter appearance
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class PlayerAnalyticsGUI:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("Tomb Player Analytics - Real-time Monitor")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)

        # Monitoring state
        self.monitoring = False
        self.monitor_thread = None
        self.player_data = {}
        self.my_position = None
        self.update_lock = threading.Lock()

        # Statistics
        self.stats = {
            'total_players': 0,
            'avg_distance': 0.0,
            'closest_player': None,
            'furthest_player': None,
            'last_update': None
        }

        self.setup_gui()
        self.update_my_position()

    def setup_gui(self):
        """Setup the main GUI components."""
        # Main container
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Title
        self.title_label = ctk.CTkLabel(
            self.main_frame,
            text="🏺 Tomb Player Analytics",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.title_label.pack(pady=(10, 20))

        # Control panel
        self.setup_control_panel()

        # Statistics panel
        self.setup_stats_panel()

        # Player list
        self.setup_player_list()

        # Status bar
        self.setup_status_bar()

    def setup_control_panel(self):
        """Setup the control panel with start/stop buttons."""
        control_frame = ctk.CTkFrame(self.main_frame)
        control_frame.pack(fill="x", padx=10, pady=(0, 10))

        # Start/Stop button
        self.control_button = ctk.CTkButton(
            control_frame,
            text="▶ Start Monitoring",
            command=self.toggle_monitoring,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            fg_color=("green", "darkgreen"),
            hover_color=("lightgreen", "green")
        )
        self.control_button.pack(side="left", padx=10, pady=10)

        # Position display
        self.position_label = ctk.CTkLabel(
            control_frame,
            text="Position: Not available",
            font=ctk.CTkFont(size=12)
        )
        self.position_label.pack(side="right", padx=10, pady=10)

    def setup_stats_panel(self):
        """Setup the statistics panel."""
        stats_frame = ctk.CTkFrame(self.main_frame)
        stats_frame.pack(fill="x", padx=10, pady=(0, 10))

        # Stats title
        stats_title = ctk.CTkLabel(
            stats_frame,
            text="📊 Statistics",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        stats_title.pack(pady=(10, 5))

        # Stats container
        stats_container = ctk.CTkFrame(stats_frame)
        stats_container.pack(fill="x", padx=10, pady=(0, 10))

        # Create stats labels
        self.stats_labels = {}
        stats_info = [
            ("total", "Total Players: 0"),
            ("avg_distance", "Avg Distance: 0.0m"),
            ("closest", "Closest: None"),
            ("furthest", "Furthest: None"),
            ("last_update", "Last Update: Never")
        ]

        for i, (key, text) in enumerate(stats_info):
            label = ctk.CTkLabel(
                stats_container,
                text=text,
                font=ctk.CTkFont(size=12)
            )
            label.grid(row=i//3, column=i%3, padx=10, pady=5, sticky="w")
            self.stats_labels[key] = label

    def setup_player_list(self):
        """Setup the scrollable player list."""
        # Player list frame
        list_frame = ctk.CTkFrame(self.main_frame)
        list_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))

        # List title
        list_title = ctk.CTkLabel(
            list_frame,
            text="👥 Nearby Players",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        list_title.pack(pady=(10, 5))

        # Scrollable frame for players
        self.player_scroll_frame = ctk.CTkScrollableFrame(
            list_frame,
            height=300,
            fg_color="transparent"
        )
        self.player_scroll_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))

        # No players message (initially shown)
        self.no_players_label = ctk.CTkLabel(
            self.player_scroll_frame,
            text="No players detected\nStart monitoring to see nearby players",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        self.no_players_label.pack(pady=50)

    def setup_status_bar(self):
        """Setup the status bar."""
        self.status_frame = ctk.CTkFrame(self.main_frame, height=30)
        self.status_frame.pack(fill="x", padx=10, pady=(0, 10))
        self.status_frame.pack_propagate(False)

        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="Ready to start monitoring",
            font=ctk.CTkFont(size=11),
            anchor="w"
        )
        self.status_label.pack(side="left", padx=10, pady=5)

    def update_my_position(self):
        """Update the current player position."""
        try:
            player_list = minescript.players()
            self.my_position = next(player.position for player in player_list if player.local)
            pos_text = f"Position: [{self.my_position[0]:.1f}, {self.my_position[1]:.1f}, {self.my_position[2]:.1f}]"
            self.position_label.configure(text=pos_text)
            return True
        except Exception as e:
            self.position_label.configure(text="Position: Error getting position")
            self.update_status(f"Error getting position: {e}", "error")
            return False

    def toggle_monitoring(self):
        """Toggle the monitoring state."""
        if not self.monitoring:
            self.start_monitoring()
        else:
            self.stop_monitoring()

    def start_monitoring(self):
        """Start the monitoring thread."""
        if not self.update_my_position():
            return

        self.monitoring = True
        self.control_button.configure(
            text="⏸ Stop Monitoring",
            fg_color=("red", "darkred"),
            hover_color=("lightcoral", "red")
        )

        # Start monitoring thread
        self.monitor_thread = threading.Thread(target=self.monitor_players, daemon=True)
        self.monitor_thread.start()

        self.update_status("Monitoring started", "success")

    def stop_monitoring(self):
        """Stop the monitoring."""
        self.monitoring = False
        self.control_button.configure(
            text="▶ Start Monitoring",
            fg_color=("green", "darkgreen"),
            hover_color=("lightgreen", "green")
        )

        self.update_status("Monitoring stopped", "info")

    def monitor_players(self):
        """Background thread function to monitor players."""
        while self.monitoring:
            try:
                # Update position
                self.update_my_position()

                # Get nearby players
                player_list = minescript.players(max_distance=100, sort="nearest")

                # Filter out local player and players with 3 digits in name
                nearby_players = [
                    player for player in player_list
                    if not player.local and not re.search(r'.*\d{3}.*', player.name)
                ]

                # Process player data
                new_player_data = {}
                for player in nearby_players:
                    player_info = self.process_player(player)
                    new_player_data[player.name] = player_info

                # Thread-safe update
                with self.update_lock:
                    self.player_data = new_player_data
                    self.update_statistics()

                # Schedule GUI update on main thread
                self.root.after(0, self.update_player_display)

                time.sleep(1)  # Update every second

            except Exception as e:
                self.root.after(0, lambda: self.update_status(f"Monitor error: {e}", "error"))
                time.sleep(2)  # Wait longer on error

    def process_player(self, player) -> Dict:
        """Process a single player and return their data."""
        if not self.my_position:
            return {}

        coords = [round(coord) for coord in player.position]

        # Calculate distance
        distance = math.sqrt(
            (player.position[0] - self.my_position[0]) ** 2 +
            (player.position[1] - self.my_position[1]) ** 2 +
            (player.position[2] - self.my_position[2]) ** 2
        )

        # Determine direction
        direction = self.get_direction(player.position, self.my_position)

        return {
            "coords": coords,
            "distance": round(distance, 1),
            "direction": direction,
            "position": player.position
        }

    def get_direction(self, target_position, my_position):
        """Determine direction from my position to target position."""
        dx = target_position[0] - my_position[0]
        dz = target_position[2] - my_position[2]

        if abs(dx) > abs(dz):
            primary = "east" if dx > 0 else "west"
        else:
            primary = "south" if dz > 0 else "north"

        # Add diagonal directions for better precision
        if abs(dx) > 0.5 and abs(dz) > 0.5:
            if dx > 0 and dz > 0:
                return "southeast"
            elif dx > 0 and dz < 0:
                return "northeast"
            elif dx < 0 and dz > 0:
                return "southwest"
            elif dx < 0 and dz < 0:
                return "northwest"
        return primary

    def update_statistics(self):
        """Update statistics based on current player data."""
        if not self.player_data:
            self.stats = {
                'total_players': 0,
                'avg_distance': 0.0,
                'closest_player': None,
                'furthest_player': None,
                'last_update': datetime.now().strftime("%H:%M:%S")
            }
            return

        distances = [data["distance"] for data in self.player_data.values()]
        sorted_by_distance = sorted(self.player_data.items(), key=lambda x: x[1]["distance"])

        self.stats = {
            'total_players': len(self.player_data),
            'avg_distance': sum(distances) / len(distances),
            'closest_player': sorted_by_distance[0] if sorted_by_distance else None,
            'furthest_player': sorted_by_distance[-1] if sorted_by_distance else None,
            'last_update': datetime.now().strftime("%H:%M:%S")
        }

    def update_player_display(self):
        """Update the player display in the GUI (called on main thread)."""
        # Clear existing player widgets
        for widget in self.player_scroll_frame.winfo_children():
            widget.destroy()

        with self.update_lock:
            player_data_copy = self.player_data.copy()
            stats_copy = self.stats.copy()

        # Update statistics display
        self.update_stats_display(stats_copy)

        if not player_data_copy:
            # Show no players message
            self.no_players_label = ctk.CTkLabel(
                self.player_scroll_frame,
                text="No players detected nearby\nTry moving to a more populated area",
                font=ctk.CTkFont(size=14),
                text_color="gray"
            )
            self.no_players_label.pack(pady=50)
            return

        # Sort players by distance
        sorted_players = sorted(player_data_copy.items(), key=lambda x: x[1]["distance"])

        # Create player widgets
        for name, data in sorted_players:
            self.create_player_widget(name, data)

    def create_player_widget(self, name: str, data: Dict):
        """Create a widget for displaying player information."""
        # Determine color based on distance
        distance = data["distance"]
        if distance < 20:
            color = ("green", "darkgreen")
        elif distance < 50:
            color = ("orange", "darkorange")
        else:
            color = ("red", "darkred")

        # Player frame
        player_frame = ctk.CTkFrame(self.player_scroll_frame, fg_color=color)
        player_frame.pack(fill="x", padx=5, pady=2)

        # Player info
        coords = data["coords"]
        direction = data["direction"]

        # Name and distance (main info)
        main_info = ctk.CTkLabel(
            player_frame,
            text=f"👤 {name} - {distance}m away",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="white"
        )
        main_info.pack(anchor="w", padx=10, pady=(5, 0))

        # Coordinates and direction
        detail_info = ctk.CTkLabel(
            player_frame,
            text=f"📍 [{coords[0]}, {coords[1]}, {coords[2]}] | 🧭 {direction.title()}",
            font=ctk.CTkFont(size=11),
            text_color="lightgray"
        )
        detail_info.pack(anchor="w", padx=10, pady=(0, 5))

    def update_stats_display(self, stats: Dict):
        """Update the statistics display."""
        self.stats_labels["total"].configure(text=f"Total Players: {stats['total_players']}")
        self.stats_labels["avg_distance"].configure(text=f"Avg Distance: {stats['avg_distance']:.1f}m")

        closest = stats['closest_player']
        if closest:
            closest_text = f"Closest: {closest[0]} ({closest[1]['distance']:.1f}m)"
        else:
            closest_text = "Closest: None"
        self.stats_labels["closest"].configure(text=closest_text)

        furthest = stats['furthest_player']
        if furthest:
            furthest_text = f"Furthest: {furthest[0]} ({furthest[1]['distance']:.1f}m)"
        else:
            furthest_text = "Furthest: None"
        self.stats_labels["furthest"].configure(text=furthest_text)

        self.stats_labels["last_update"].configure(text=f"Last Update: {stats['last_update']}")

    def update_status(self, message: str, status_type: str = "info"):
        """Update the status bar."""
        colors = {
            "info": "white",
            "success": "lightgreen",
            "error": "lightcoral",
            "warning": "orange"
        }

        self.status_label.configure(
            text=message,
            text_color=colors.get(status_type, "white")
        )

    def run(self):
        """Start the GUI application."""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.stop_monitoring()
        finally:
            if self.monitoring:
                self.stop_monitoring()

# Main execution
if __name__ == "__main__":
    try:
        app = PlayerAnalyticsGUI()
        app.run()
    except Exception as e:
        minescript.echo(f"§cError starting GUI: {e}")
        print(f"Error: {e}")
